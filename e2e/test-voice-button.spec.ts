import { test, expect } from '@playwright/test';

test('floating voice button opens RealtimeVoiceAssistant', async ({ page }) => {
  // Navigate to the app
  await page.goto('http://localhost:5177');
  
  // Wait for page to load
  await page.waitForLoadState('networkidle');
  
  // Handle authentication - fill in login form
  await page.fill('input[placeholder*="email"]', '<EMAIL>');
  await page.fill('input[placeholder*="password"]', 'coolguy99');
  await page.click('button:has-text("Sign in")');
  
  // Wait for redirect to dashboard after login
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(3000); // Give more time for components to load
  
  // Look for the floating voice button with increased timeout
  const floatingButton = page.locator('[data-testid="floating-voice-button"]');
  await expect(floatingButton).toBeVisible({ timeout: 10000 });
  
  // Click the floating voice button
  await floatingButton.click();
  
  // Wait a moment for the click to register
  await page.waitForTimeout(1000);
  
  // Take a screenshot to debug what's happening
  await page.screenshot({ path: 'after-click-debug.png' });
  
  // Check if the button changed color (red when open)
  const buttonAfterClick = page.locator('[data-testid="floating-voice-button"]');
  await expect(buttonAfterClick).toHaveClass(/bg-red-500/);
  
  // Wait for the voice assistant overlay to appear with longer timeout
  const overlay = page.locator('[data-testid="voice-assistant-overlay"]');
  await expect(overlay).toBeVisible({ timeout: 10000 });
  
  // Check if RealtimeVoiceAssistant content is loaded
  // Look for the "Voice Assistant" heading that should be in RealtimeVoiceAssistant
  const voiceAssistantHeading = page.locator('h2:has-text("Voice Assistant")');
  await expect(voiceAssistantHeading).toBeVisible();

  // Verify the GA "Connect Assistant" control renders
  const connectAssistantButton = page.locator('[data-testid="connect-assistant-button"]');
  await expect(connectAssistantButton).toBeVisible();
  await expect(connectAssistantButton).toBeEnabled();

  // Look for the Connect button
  const connectButton = page.locator('button:has-text("Connect Voice Assistant")');
  await expect(connectButton).toBeVisible();

  // Take a screenshot to see what's rendered
  await page.screenshot({ path: 'voice-assistant-test.png' });
  
  console.log('✅ Floating voice button successfully opens RealtimeVoiceAssistant!');
});

test('voice assistant connect button functionality', async ({ page }) => {
  // Navigate to the app
  await page.goto('http://localhost:5177');
  
  // Wait for page to load
  await page.waitForLoadState('networkidle');
  
  // Handle authentication - fill in login form
  await page.fill('input[placeholder*="email"]', '<EMAIL>');
  await page.fill('input[placeholder*="password"]', 'coolguy99');
  await page.click('button:has-text("Sign in")');
  
  // Wait for redirect to dashboard after login
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(3000); // Give more time for components to load
  
  // Click the floating voice button with increased timeout
  const floatingButton = page.locator('[data-testid="floating-voice-button"]');
  await expect(floatingButton).toBeVisible({ timeout: 10000 });
  await floatingButton.click();
  
  // Wait for overlay
  const overlay = page.locator('[data-testid="voice-assistant-overlay"]');
  await expect(overlay).toBeVisible();

  const connectAssistantButton = page.locator('[data-testid="connect-assistant-button"]');
  await expect(connectAssistantButton).toBeVisible();
  await expect(connectAssistantButton).toBeEnabled();

  // Find and click the Connect button
  const connectButton = page.locator('button:has-text("Connect Voice Assistant")');
  await connectButton.click();
  
  // Wait a moment for connection attempt
  await page.waitForTimeout(2000);
  
  // Check if status changes to "Connected" or shows connection attempt
  // Look for the connection status indicator in the header
  const connectedStatus = page.locator('text=Connected').first();
  const disconnectedStatus = page.locator('text=Disconnected').first();

  // Take screenshot of connection attempt
  await page.screenshot({ path: 'voice-assistant-connect-test.png' });

  // Check if either connected or disconnected status is visible
  try {
    await expect(connectedStatus.or(disconnectedStatus)).toBeVisible({ timeout: 5000 });
    const isConnected = await connectedStatus.isVisible();
    console.log('Connection status:', isConnected ? 'Connected' : 'Disconnected');

    if (isConnected) {
      console.log('✅ Voice assistant connected successfully!');
    } else {
      console.log('⚠️ Voice assistant not connected, but UI is responsive');
    }
  } catch (error) {
    console.log('Could not determine connection status:', error);
  }

  // Check for any error messages
  const errorMessages = await page.locator('[class*="error"], [class*="Error"]').allTextContents();
  if (errorMessages.length > 0) {
    console.log('Error messages found:', errorMessages);
  }
});
