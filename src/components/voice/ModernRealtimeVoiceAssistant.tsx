import React, { useCallback, useEffect, useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>if<PERSON>, WifiOff, Database, Brain, Zap } from 'lucide-react';
import type { VoiceEventData } from '../../types/schema';
import { useModernRealtimeVoice } from '../../hooks/useModernRealtimeVoice';

interface ModernRealtimeVoiceAssistantProps {
  userId?: string;
  onEventCreated?: (event: VoiceEventData) => void;
  onError?: (error: string) => void;
}

const ModernRealtimeVoiceAssistant: React.FC<ModernRealtimeVoiceAssistantProps> = ({
  userId: _userId = 'seafood-user',
  onEventCreated,
  onError,
}) => {
  const [recentActivities, setRecentActivities] = useState<string[]>([]);
  const [ephemeralToken, setEphemeralToken] = useState<string | null>(null);
  const [assistantSession, setAssistantSession] = useState<{
    id: string;
    model: string;
    voice?: string;
  } | null>(null);
  const [isInitializingAssistant, setIsInitializingAssistant] = useState(false);

  const useRelay = String(import.meta.env.VITE_ENABLE_DIRECT_REALTIME) !== 'true';

  const addActivity = useCallback((activity: string) => {
    const timestamp = new Date().toLocaleTimeString();
    const activityWithTime = `${timestamp}: ${activity}`;
    setRecentActivities((prev) => [activityWithTime, ...prev.slice(0, 4)]);
  }, []);

  // Use the modern realtime voice hook
  const {
    isConnected,
    isListening,
    error,
    transcript,
    response,
    toolCalls,
    connect,
    disconnect,
    sendMessage,
    clearConversation,
    updateClientConfig,
  } = useModernRealtimeVoice({
    model: (import.meta.env.VITE_REALTIME_MODEL as string) ?? 'gpt-realtime',
    voice: 'alloy',
    enableDebugLogs: true,
    autoConnect: false, // We'll connect manually after getting the token or hitting the relay
    ...(useRelay
      ? {
          relayUrl: '/api/realtime-relay',
          transport: 'websocket' as const,
          useInsecureApiKey: true,
        }
      : {
          ephemeralToken: ephemeralToken ?? undefined,
          transport: 'webrtc' as const,
        }),
  });

  // Create a GA realtime assistant session and store the client secret
  const createAssistantSession = useCallback(async (): Promise<string | null> => {

    try {
      setIsInitializingAssistant(true);

      const response = await fetch('/api/voice/realtime-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: (import.meta.env.VITE_REALTIME_MODEL as string) ?? 'gpt-realtime',
          voice: 'alloy',
          instructions: 'You are the Pacific Cloud Seafoods realtime assistant. Help manage seafood inventory, HACCP tasks, and freezer monitoring with concise voice responses.',
          modalities: ['audio', 'text'],
        }),
      });

      if (!response.ok) {
        throw new Error(`Realtime session request failed: ${response.status}`);
      }

      const data = await response.json();
      const token = data.client_secret?.value ?? data.client_secret ?? data.value;

      if (!token) {
        throw new Error('Realtime session did not include a client secret');
      }

      if (import.meta.env.DEV) {
        console.debug('Realtime session created:', {
          id: data.id,
          model: data.model,
          tokenPrefix: token?.slice(0, 8),
          tokenType: token?.startsWith('ek_') ? 'ephemeral' : 'other'
        });
      }

      setEphemeralToken(token);
      setAssistantSession({
        id: data.id,
        model: data.model ?? ((import.meta.env.VITE_REALTIME_MODEL as string) ?? 'gpt-realtime'),
        voice: data.voice,
      });
      addActivity('Realtime assistant session created');
      return token;
    } catch (error) {
      console.error('Failed to create realtime assistant session:', error);
      const message = error instanceof Error ? error.message : 'Unknown assistant session error';
      onError?.(`Failed to create assistant session: ${message}`);
      return null;
    } finally {
      setIsInitializingAssistant(false);
    }
  }, [addActivity, onError]);

  // Connect to voice assistant
  const handleConnect = useCallback(async () => {
    try {
      if (!useRelay) {
        let token = ephemeralToken;

        if (!token) {
          token = await createAssistantSession();
          if (!token) return;
          // Wait one tick for state update so the hook sees the new token
          await new Promise((resolve) => setTimeout(resolve, 0));
        }

        if (token) {
          updateClientConfig({ ephemeralToken: token });
        }
      }

      const success = await connect();
      if (success) {
        addActivity('Connected to voice assistant');
      }
    } catch (error) {
      console.error('Connection failed:', error);
      onError?.(`Connection failed: ${error instanceof Error ? error.message : error}`);
    }
  }, [useRelay, ephemeralToken, createAssistantSession, connect, onError, addActivity, updateClientConfig]);

  // Disconnect from voice assistant
  const handleDisconnect = useCallback(async () => {
    await disconnect();
    if (!useRelay) {
      setAssistantSession(null);
      setEphemeralToken(null);
    }
    addActivity('Disconnected from voice assistant');
  }, [addActivity, disconnect, useRelay]);

  // Handle tool calls for inventory events
  useEffect(() => {
    if (toolCalls.length > 0) {
      const latestToolCall = toolCalls[toolCalls.length - 1];
      
      // Process the tool call result
      try {
        const result = typeof latestToolCall.result === 'string' 
          ? JSON.parse(latestToolCall.result)
          : latestToolCall.result;

        // Create voice event data if applicable
        if (latestToolCall.toolName === 'add_inventory_item' && result.success) {
          const eventData: VoiceEventData = {
            event_type: 'receiving',
            species: latestToolCall.args.species as string,
            quantity: latestToolCall.args.quantity as number,
            unit: latestToolCall.args.unit as string,
            location: latestToolCall.args.location as string,
            vendor: latestToolCall.args.vendor as string,
            received_date: latestToolCall.args.receivedDate as string,
            expiration_date: latestToolCall.args.expirationDate as string,
            price_per_unit: latestToolCall.args.pricePerUnit as number,
            quality_grade: latestToolCall.args.qualityGrade as string,
            user_id: _userId,
            created_at: new Date().toISOString(),
          };
          
          onEventCreated?.(eventData);
        }

        addActivity(`Tool executed: ${latestToolCall.toolName}`);
      } catch (error) {
        console.error('Error processing tool call result:', error);
      }
    }
  }, [toolCalls, onEventCreated, _userId, addActivity]);

  // Handle errors
  useEffect(() => {
    if (error) {
      onError?.(error);
      addActivity(`Error: ${error}`);
    }
  }, [error, onError, addActivity]);

  // Test message function
  const sendTestMessage = useCallback(async () => {
    try {
      await sendMessage("Hi, how much COD do we have in stock?");
      addActivity('Sent test message');
    } catch (error) {
      console.error('Failed to send test message:', error);
    }
  }, [sendMessage, addActivity]);

  return (
    <div className="bg-white rounded-lg shadow-lg p-6 max-w-2xl mx-auto">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-2">
          <Brain className="w-6 h-6 text-blue-600" />
          <h2 className="text-xl font-semibold text-gray-900">
            Modern Voice Assistant
          </h2>
          <Zap className="w-5 h-5 text-yellow-500" />
        </div>
        
        <div className="flex items-center space-x-2">
          {isConnected ? (
            <Wifi className="w-5 h-5 text-green-500" />
          ) : (
            <WifiOff className="w-5 h-5 text-red-500" />
          )}
          <span className={`text-sm font-medium ${
            isConnected ? 'text-green-600' : 'text-red-600'
          }`}>
            {isConnected ? 'Connected' : 'Disconnected'}
          </span>
        </div>
      </div>

      <div className="flex items-center space-x-3 mb-4">
        <button
          onClick={createAssistantSession}
          disabled={isInitializingAssistant || Boolean(assistantSession)}
          className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
            assistantSession
              ? 'bg-green-600 text-white cursor-default'
              : 'bg-indigo-600 text-white hover:bg-indigo-700'
          } ${
            isInitializingAssistant ? 'opacity-70 cursor-not-allowed' : ''
          }`}
          data-testid="connect-assistant-button"
        >
          <Brain className="w-4 h-4" />
          <span>
            {assistantSession
              ? 'Assistant Ready'
              : isInitializingAssistant
                ? 'Connecting…'
                : 'Connect Assistant'}
          </span>
        </button>

        {assistantSession && (
          <div className="text-xs text-gray-600">
            <div className="font-medium text-gray-700">Session ID: {assistantSession.id}</div>
            <div>
              Model: {assistantSession.model}
              {assistantSession.voice ? ` · Voice: ${assistantSession.voice}` : ''}
            </div>
          </div>
        )}

        {useRelay && (
          <div className="text-xs text-gray-500 max-w-xs">
            Proxy mode active. Session tokens are prepared via the relay before WebSocket
            connection.
          </div>
        )}
      </div>

      {/* Connection Controls */}
      <div className="flex space-x-3 mb-6">
        {!isConnected ? (
          <button
            onClick={handleConnect}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Mic className="w-4 h-4" />
            <span>Connect Voice Assistant</span>
          </button>
        ) : (
          <button
            onClick={handleDisconnect}
            className="flex items-center space-x-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
          >
            <MicOff className="w-4 h-4" />
            <span>Disconnect</span>
          </button>
        )}

        {isConnected && (
          <>
            <button
              onClick={sendTestMessage}
              className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              Test Message
            </button>
            <button
              onClick={clearConversation}
              className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              Clear
            </button>
          </>
        )}
      </div>

      {/* Status Indicators */}
      <div className="grid grid-cols-2 gap-4 mb-6">
        <div className="flex items-center space-x-2 p-3 bg-gray-50 rounded-lg">
          <div className={`w-3 h-3 rounded-full ${
            isListening ? 'bg-red-500 animate-pulse' : 'bg-gray-300'
          }`} />
          <span className="text-sm font-medium">
            {isListening ? 'Listening...' : 'Ready'}
          </span>
        </div>
        
        <div className="flex items-center space-x-2 p-3 bg-gray-50 rounded-lg">
          <Database className="w-4 h-4 text-blue-600" />
          <span className="text-sm font-medium">
            Tools: {toolCalls.length}
          </span>
        </div>
      </div>

      {/* Conversation Display */}
      <div className="space-y-4 mb-6">
        {transcript && (
          <div className="p-3 bg-blue-50 rounded-lg">
            <div className="text-sm font-medium text-blue-800 mb-1">You said:</div>
            <div className="text-blue-700">{transcript}</div>
          </div>
        )}

        {response && (
          <div className="p-3 bg-green-50 rounded-lg">
            <div className="text-sm font-medium text-green-800 mb-1">Assistant:</div>
            <div className="text-green-700">{response}</div>
          </div>
        )}

        {error && (
          <div className="p-3 bg-red-50 rounded-lg">
            <div className="text-sm font-medium text-red-800 mb-1">Error:</div>
            <div className="text-red-700">{error}</div>
          </div>
        )}
      </div>

      {/* Tool Calls Display */}
      {toolCalls.length > 0 && (
        <div className="mb-6">
          <h3 className="text-sm font-medium text-gray-700 mb-2">Recent Tool Calls:</h3>
          <div className="space-y-2 max-h-32 overflow-y-auto">
            {toolCalls.slice(-3).map((toolCall, index) => (
              <div key={index} className="p-2 bg-purple-50 rounded text-xs">
                <div className="font-medium text-purple-800">{toolCall.toolName}</div>
                <div className="text-purple-600 truncate">
                  {JSON.stringify(toolCall.args)}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Recent Activities */}
      <div>
        <h3 className="text-sm font-medium text-gray-700 mb-2">Recent Activities:</h3>
        <div className="space-y-1 max-h-32 overflow-y-auto">
          {recentActivities.map((activity, index) => (
            <div key={index} className="text-xs text-gray-600 p-2 bg-gray-50 rounded">
              {activity}
            </div>
          ))}
        </div>
      </div>

      {/* Instructions */}
      <div className="mt-6 p-4 bg-blue-50 rounded-lg">
        <h4 className="text-sm font-medium text-blue-800 mb-2">Using the 2025 OpenAI Agents SDK:</h4>
        <ul className="text-xs text-blue-700 space-y-1">
          <li>• Click "Connect Voice Assistant" to start</li>
          <li>• Speak naturally about seafood inventory</li>
          <li>• The assistant will automatically handle microphone and audio</li>
          <li>• Try: "Add 5 pounds of salmon to inventory"</li>
          <li>• Or: "How much COD do we have in stock?"</li>
        </ul>
      </div>
    </div>
  );
};

export default ModernRealtimeVoiceAssistant;
