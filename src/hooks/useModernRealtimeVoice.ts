import { useState, useEffect, useRef, useCallback } from 'react';
import { ModernRealtimeVoiceClient, ModernVoiceClientConfig, createModernRealtimeVoiceClient } from '../lib/ModernRealtimeVoiceClient';

/**
 * Modern React hook for OpenAI Realtime Voice processing using the 2025 Agents SDK
 * Uses RealtimeAgent and RealtimeSession for simplified voice interactions
 */

export interface UseModernRealtimeVoiceOptions {
  apiKey?: string;
  ephemeralToken?: string;
  relayUrl?: string;
  transport?: 'webrtc' | 'websocket';
  useInsecureApiKey?: boolean;
  model?: string;
  voice?: 'alloy' | 'echo' | 'fable' | 'onyx' | 'nova' | 'shimmer';
  enableDebugLogs?: boolean;
  autoConnect?: boolean;
}

export interface ModernRealtimeVoiceState {
  isConnected: boolean;
  isListening: boolean;
  error: string | null;
  transcript: string;
  response: string;
  toolCalls: Array<{
    toolName: string;
    args: Record<string, unknown>;
    result: unknown;
    timestamp: number;
  }>;
  // Performance metrics
  latency?: number;
  connectionAttempts: number;
}

export function useModernRealtimeVoice(options: UseModernRealtimeVoiceOptions = {}) {
  const [state, setState] = useState<ModernRealtimeVoiceState>({
    isConnected: false,
    isListening: false,
    error: null,
    transcript: '',
    response: '',
    toolCalls: [],
    connectionAttempts: 0,
  });

  const clientRef = useRef<ModernRealtimeVoiceClient | null>(null);
  const isInitializedRef = useRef(false);
  const retryCountRef = useRef(0);
  const maxRetries = 3;
  const retryDelay = 1000; // Base delay in ms

  // Initialize client
  const initializeClient = useCallback(() => {
    if (isInitializedRef.current || clientRef.current) {
      return;
    }

    try {
      const config: ModernVoiceClientConfig = {
        apiKey: options.apiKey,
        ephemeralToken: options.ephemeralToken,
        relayUrl: options.relayUrl,
        transport: options.transport,
        useInsecureApiKey: options.useInsecureApiKey,
        model: options.model ?? 'gpt-4o-realtime-preview-2024-12-17',
        voice: options.voice ?? 'alloy',
        enableDebugLogs: options.enableDebugLogs ?? false,
      };

      if (config.relayUrl && !config.apiKey && !config.ephemeralToken) {
        config.apiKey = 'relay';
        config.useInsecureApiKey = true;
      }

      const missingCredentials =
        !config.relayUrl && !config.apiKey && !config.ephemeralToken;

      // Wait until credentials are provided before initializing the client
      if (missingCredentials) {
        if (config.enableDebugLogs) {
          console.debug('useModernRealtimeVoice: awaiting apiKey or ephemeralToken before init');
        }
        return;
      }

      const events = {
        onConnected: () => {
          setState(prev => ({ ...prev, isConnected: true, error: null }));
        },
        onDisconnected: () => {
          setState(prev => ({ ...prev, isConnected: false, isListening: false }));
        },
        onListening: () => {
          setState(prev => ({ ...prev, isListening: true }));
        },
        onProcessing: () => {
          setState(prev => ({ ...prev, isListening: false }));
        },
        onTranscript: (transcript: string, isFinal: boolean) => {
          if (isFinal) {
            setState(prev => ({ ...prev, transcript }));
          }
        },
        onResponse: (text: string) => {
          setState(prev => ({ ...prev, response: text }));
        },
        onError: (error: string) => {
          setState(prev => ({ ...prev, error, isConnected: false, isListening: false }));
        },
        onToolCall: (toolName: string, args: Record<string, unknown>, result: unknown) => {
          const toolCall = {
            toolName,
            args,
            result,
            timestamp: Date.now(),
          };
          setState(prev => ({
            ...prev,
            toolCalls: [...prev.toolCalls, toolCall],
          }));
        },
      };

      clientRef.current = createModernRealtimeVoiceClient(config, events);
      isInitializedRef.current = true;

      if (config.enableDebugLogs) {
        console.log('🎤 Modern RealtimeVoice client initialized');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to initialize client';
      setState(prev => ({ ...prev, error: errorMessage }));
      console.error('Failed to initialize Modern RealtimeVoice client:', error);
    }
  }, [
    options.apiKey,
    options.ephemeralToken,
    options.relayUrl,
    options.transport,
    options.useInsecureApiKey,
    options.model,
    options.voice,
    options.enableDebugLogs,
  ]);

  // Connect to the service with retry logic
  const connectWithRetry = useCallback(async (retryCount = 0): Promise<boolean> => {
    const startTime = performance.now();

    setState(prev => ({ ...prev, connectionAttempts: prev.connectionAttempts + 1 }));

    if (!clientRef.current) {
      initializeClient();
    }

    if (!clientRef.current) {
      if (!options.apiKey && !options.ephemeralToken && !options.relayUrl) {
        return false;
      }
      setState(prev => ({ ...prev, error: 'Failed to initialize client' }));
      return false;
    }

    try {
      setState(prev => ({ ...prev, error: null }));
      const success = await clientRef.current.connect();
      if (success) {
        const latency = performance.now() - startTime;
        setState(prev => ({ ...prev, latency }));
        retryCountRef.current = 0; // Reset retry count on success
        return true;
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Connection failed';

      if (retryCount < maxRetries) {
        const delay = retryDelay * Math.pow(2, retryCount); // Exponential backoff
        setState(prev => ({
          ...prev,
          error: `Connection failed, retrying in ${delay}ms... (${retryCount + 1}/${maxRetries})`
        }));

        await new Promise(resolve => setTimeout(resolve, delay));
        return connectWithRetry(retryCount + 1);
      } else {
        setState(prev => ({ ...prev, error: `${errorMessage} (failed after ${maxRetries} retries)` }));
        return false;
      }
    }
    return false;
  }, [initializeClient, options.apiKey, options.ephemeralToken, options.relayUrl]);

  // Public connect function
  const connect = useCallback(async (): Promise<boolean> => {
    return connectWithRetry(0);
  }, [connectWithRetry]);

  // Disconnect from the service
  const disconnect = useCallback(async (): Promise<void> => {
    if (clientRef.current) {
      await clientRef.current.disconnect();
    }
  }, []);

  const updateClientConfig = useCallback((config: Partial<ModernVoiceClientConfig>) => {
    if (!clientRef.current) {
      initializeClient();
    }
    clientRef.current?.updateConfig(config);
  }, [initializeClient]);

  // Send a text message (useful for testing)
  const sendMessage = useCallback(async (message: string): Promise<void> => {
    if (!clientRef.current || !state.isConnected) {
      throw new Error('Not connected to voice service');
    }
    
    await clientRef.current.sendMessage(message);
  }, [state.isConnected]);

  // Clear tool calls
  const clearToolCalls = useCallback(() => {
    setState(prev => ({ ...prev, toolCalls: [] }));
  }, []);

  // Clear conversation using official RealtimeSession history management
  const clearConversation = useCallback(() => {
    // Clear local state
    setState(prev => ({
      ...prev,
      transcript: '',
      response: '',
      toolCalls: []
    }));

    // Clear session history using official SDK method
    if (clientRef.current) {
      clientRef.current.clearConversationHistory();
    }
  }, []);

  // Auto-connect if enabled
  useEffect(() => {
    if (options.autoConnect && !state.isConnected && !state.error) {
      connect();
    }
  }, [options.autoConnect, state.isConnected, state.error, connect]);

  // Initialize client on mount
  useEffect(() => {
    initializeClient();
    
    return () => {
      if (clientRef.current) {
        clientRef.current.disconnect();
        clientRef.current = null;
        isInitializedRef.current = false;
      }
    };
  }, [initializeClient]);

  return {
    // State
    isConnected: state.isConnected,
    isListening: state.isListening,
    error: state.error,
    transcript: state.transcript,
    response: state.response,
    toolCalls: state.toolCalls,

    // Actions
    connect,
    disconnect,
    sendMessage,
    clearToolCalls,
    clearConversation,
    updateClientConfig,

    // Utils
    getConnectionStatus: () => clientRef.current?.getConnectionStatus() ?? false,
    getConversationHistory: () => clientRef.current?.getConversationHistory() ?? [],
  };
}
