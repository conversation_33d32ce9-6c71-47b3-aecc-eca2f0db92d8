import { tool } from '@openai/agents/realtime';
import { z } from 'zod';
import { supabase } from './supabase';
import { tempStickService } from './tempstick-service';

/**
 * Seafood Inventory Tools for OpenAI Realtime API Function Calling
 * Uses the official @openai/agents/realtime tool() function
 * These tools enable the AI assistant to perform inventory operations
 */

export interface InventoryEvent {
  event_type: 'receiving' | 'sale' | 'disposal' | 'physical_count';
  product_name: string;
  category: string;
  quantity: number;
  unit: string;
  unit_price?: number;
  unit_cost?: number;
  total_amount?: number;
  vendor?: string;
  customer?: string;
  notes?: string;
  temperature?: number;
  condition?: string;
  lot_number?: string;
  expiration_date?: string;
}

export interface InventoryQuery {
  product_name?: string;
  category?: string;
  event_type?: string;
  date_range?: {
    start: string;
    end: string;
  };
}

/**
 * Official OpenAI Agents SDK Tool Definitions
 * Using the tool() function from @openai/agents/realtime
 */

// Query inventory tool using official SDK pattern
export const queryInventoryTool = tool({
  name: 'query_inventory',
  description: 'Query seafood inventory to check stock levels, recent events, and product information',
  parameters: z.object({
    product_name: z.string().nullable().optional().describe('Name of the seafood product to search for'),
    category: z.enum(['finfish', 'shellfish', 'crustaceans', 'specialty']).nullable().optional().describe('Product category to filter by'),
    event_type: z.enum(['receiving', 'sale', 'disposal', 'physical_count']).nullable().optional().describe('Type of inventory event to filter by'),
    date_range: z.object({
      start: z.string().describe('Start date in ISO format'),
      end: z.string().describe('End date in ISO format')
    }).nullable().optional().describe('Date range to filter events')
  }),
  async execute(params) {
    return await executeInventoryQuery(params);
  }
});

// Add inventory tool using official SDK pattern
export const addInventoryTool = tool({
  name: 'create_inventory_event',
  description: 'Create a new inventory event (receiving, sale, disposal, or physical count) for seafood products',
  parameters: z.object({
    event_type: z.enum(['receiving', 'sale', 'disposal', 'physical_count']).describe('Type of inventory event'),
    product_name: z.string().describe('Name of the seafood product (e.g., Atlantic Salmon, King Crab, Pacific Halibut)'),
    category: z.enum(['finfish', 'shellfish', 'crustaceans', 'specialty']).describe('Product category'),
    quantity: z.number().describe('Quantity of product'),
    unit: z.string().describe('Unit of measurement (lbs, kg, cases, dozens, pieces, each)'),
    unit_price: z.number().nullable().optional().describe('Price per unit'),
    unit_cost: z.number().nullable().optional().describe('Cost per unit'),
    total_amount: z.number().nullable().optional().describe('Total transaction amount'),
    vendor: z.string().nullable().optional().describe('Vendor or supplier name'),
    customer: z.string().nullable().optional().describe('Customer name for sales'),
    notes: z.string().nullable().optional().describe('Additional notes or comments'),
    temperature: z.number().nullable().optional().describe('Product temperature in Fahrenheit'),
    condition: z.string().nullable().optional().describe('Product condition (Fresh, Frozen, Live, etc.)'),
    lot_number: z.string().nullable().optional().describe('Lot or batch number'),
    expiration_date: z.string().nullable().optional().describe('Expiration date in ISO format')
  }),
  async execute(params) {
    return await executeInventoryEvent(params);
  }
});

// Update inventory tool using official SDK pattern
export const updateInventoryTool = tool({
  name: 'update_inventory_event',
  description: 'Update an existing inventory event with new information',
  parameters: z.object({
    event_id: z.string().describe('ID of the inventory event to update'),
    updates: z.object({
      product_name: z.string().nullable().optional(),
      category: z.enum(['finfish', 'shellfish', 'crustaceans', 'specialty']).nullable().optional(),
      quantity: z.number().nullable().optional(),
      unit: z.string().nullable().optional(),
      unit_price: z.number().nullable().optional(),
      unit_cost: z.number().nullable().optional(),
      total_amount: z.number().nullable().optional(),
      vendor: z.string().nullable().optional(),
      customer: z.string().nullable().optional(),
      notes: z.string().nullable().optional(),
      temperature: z.number().nullable().optional(),
      condition: z.string().nullable().optional(),
      lot_number: z.string().nullable().optional(),
      expiration_date: z.string().nullable().optional()
    }).describe('Fields to update')
  }),
  async execute(params) {
    return await executeInventoryUpdate(params);
  }
});

// Temperature monitoring tool using official SDK pattern
export const getTemperatureTool = tool({
  name: 'get_temperature',
  description: 'Get current temperature readings from TempStick sensors for cold storage monitoring',
  parameters: z.object({
    sensor_id: z.string().nullable().optional().describe('Specific sensor ID to query'),
    location: z.string().nullable().optional().describe('Location name to filter sensors')
  }),
  async execute(params) {
    return await executeTemperatureQuery(params);
  }
});

/**
 * Legacy tool definitions for backward compatibility
 * These will be deprecated in favor of the official SDK tools above
 */
export const SEAFOOD_INVENTORY_TOOLS = [
  {
    type: 'function' as const,
    name: 'create_inventory_event',
    description: 'Create a new inventory event (receiving, sale, disposal, or physical count) for seafood products',
    parameters: {
      type: 'object',
      properties: {
        event_type: {
          type: 'string',
          enum: ['receiving', 'sale', 'disposal', 'physical_count'],
          description: 'Type of inventory event'
        },
        product_name: {
          type: 'string',
          description: 'Name of the seafood product (e.g., Atlantic Salmon, King Crab, Pacific Halibut)'
        },
        category: {
          type: 'string',
          enum: ['finfish', 'shellfish', 'crustaceans', 'specialty'],
          description: 'Product category'
        },
        quantity: {
          type: 'number',
          description: 'Quantity of product'
        },
        unit: {
          type: 'string',
          enum: ['lbs', 'kg', 'cases', 'dozens', 'pieces', 'each'],
          description: 'Unit of measurement'
        },
        unit_price: {
          type: 'number',
          description: 'Price per unit (for sales)'
        },
        unit_cost: {
          type: 'number',
          description: 'Cost per unit (for receiving)'
        },
        vendor: {
          type: 'string',
          description: 'Vendor/supplier name (for receiving events)'
        },
        customer: {
          type: 'string',
          description: 'Customer name (for sale events)'
        },
        notes: {
          type: 'string',
          description: 'Additional notes or comments'
        },
        temperature: {
          type: 'number',
          description: 'Temperature reading in Fahrenheit'
        },
        condition: {
          type: 'string',
          enum: ['excellent', 'good', 'fair', 'poor', 'damaged'],
          description: 'Product condition assessment'
        },
        lot_number: {
          type: 'string',
          description: 'Lot or batch number for traceability'
        },
        expiration_date: {
          type: 'string',
          description: 'Expiration date in YYYY-MM-DD format'
        }
      },
      required: ['event_type', 'product_name', 'category', 'quantity', 'unit']
    }
  },
  {
    type: 'function' as const,
    name: 'get_freezer_temperatures',
    description: 'Get current temperatures for freezer units from TempStick sensors',
    parameters: {
      type: 'object',
      properties: {
        unit: {
          type: 'string',
          enum: ['F', 'C'],
          description: 'Temperature unit to return (default: F)'
        },
        name_contains: {
          type: 'string',
          description: 'Optional name filter (e.g., "freezer", "walk in")'
        }
      }
    }
  },
  {
    type: 'function' as const,
    name: 'query_inventory',
    description: 'Query current inventory levels and recent events for seafood products',
    parameters: {
      type: 'object',
      properties: {
        product_name: {
          type: 'string',
          description: 'Specific product to query (optional)'
        },
        category: {
          type: 'string',
          enum: ['finfish', 'shellfish', 'crustaceans', 'specialty'],
          description: 'Product category to filter by (optional)'
        },
        event_type: {
          type: 'string',
          enum: ['receiving', 'sale', 'disposal', 'physical_count'],
          description: 'Event type to filter by (optional)'
        },
        days_back: {
          type: 'number',
          description: 'Number of days to look back (default: 7)',
          default: 7
        }
      }
    }
  },
  {
    type: 'function' as const,
    name: 'get_product_info',
    description: 'Get detailed information about a specific seafood product including specifications and handling requirements',
    parameters: {
      type: 'object',
      properties: {
        product_name: {
          type: 'string',
          description: 'Name of the seafood product to get information about'
        }
      },
      required: ['product_name']
    }
  },
  {
    type: 'function' as const,
    name: 'get_haccp_guidance',
    description: 'Get HACCP compliance guidance for seafood handling, storage temperatures, and safety requirements',
    parameters: {
      type: 'object',
      properties: {
        product_category: {
          type: 'string',
          enum: ['finfish', 'shellfish', 'crustaceans', 'specialty'],
          description: 'Product category for HACCP guidance'
        },
        processing_method: {
          type: 'string',
          enum: ['fresh', 'frozen', 'live', 'smoked', 'processed'],
          description: 'Processing method (optional)'
        }
      },
      required: ['product_category']
    }
  }
];

/**
 * Tool execution functions
 */

export async function createInventoryEvent(params: InventoryEvent): Promise<{ success: boolean; data?: unknown; message?: string; error?: string }> {
  try {
    // Prefer secure Edge Function with user identity
    try {
      const { data: fnRes, error: fnErr } = await (supabase as { functions: { invoke: (name: string, options: { body: unknown }) => Promise<{ data: unknown; error: unknown }> } }).functions.invoke('voice_inventory_event', {
        body: params,
      });
      if (!fnErr && fnRes) {
        return fnRes as { success: boolean; data?: unknown; message?: string; error?: string };
      }
      if (fnErr) {
        console.warn('voice_inventory_event function error, falling back to direct insert:', fnErr);
      }
    } catch (fxErr) {
      console.warn('voice_inventory_event invoke failed; attempting direct insert', fxErr);
    }

    // Fallback: direct insert with anon key (requires RLS to allow)
    const totalAmount = params.total_amount ?? (params.unit_price ? params.quantity * params.unit_price :
       params.unit_cost ? params.quantity * params.unit_cost : null);

    const eventData = {
      event_type: params.event_type,
      name: params.product_name,
      category: params.category,
      quantity: params.quantity,
      unit: params.unit,
      unit_price: params.unit_price,
      total_amount: totalAmount,
      notes: params.notes,
      occurred_at: new Date().toISOString(),
      created_by_voice: true,
      metadata: {
        vendor: params.vendor,
        customer: params.customer,
        temperature: params.temperature,
        condition: params.condition,
        lot_number: params.lot_number,
        expiration_date: params.expiration_date,
        voice_processed: true,
        realtime_api: true,
      },
    };

    const { data, error } = await supabase
      .from('inventory_events')
      .insert([eventData])
      .select()
      .single();

    if (error) {
      console.error('Database error:', error);
      return {
        success: false,
        error: error.message,
        message: `Failed to create ${params.event_type} event for ${params.product_name}`,
      };
    }

    return {
      success: true,
      data,
      message: `Successfully recorded ${params.event_type} of ${params.quantity} ${params.unit ?? ''} ${params.product_name}${params.vendor ? ` from ${params.vendor}` : ''}${params.customer ? ` to ${params.customer}` : ''}`.trim(),
    };

  } catch (error) {
    console.error('Error creating inventory event:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      message: 'Failed to process inventory event'
    };
  }
}

export async function queryInventory(params: InventoryQuery & { days_back?: number }): Promise<{ success: boolean; data?: unknown; message?: string; error?: string }> {
  try {
    // Prefer Edge Function to centralize logic and support RLS identity
    try {
      const { data: fnRes, error: fnErr } = await (supabase as { functions: { invoke: (name: string, options: { body: unknown }) => Promise<{ data: unknown; error: unknown }> } }).functions.invoke('query_inventory', {
        body: params,
      });
      if (!fnErr && fnRes) {
        return fnRes as { success: boolean; data?: unknown; message?: string; error?: string };
      }
      if (fnErr) {
        console.warn('query_inventory function error, falling back to direct query:', fnErr);
      }
    } catch (fxErr) {
      console.warn('query_inventory invoke failed; attempting direct query', fxErr);
    }

    // Fallback: direct query (previous behavior)
    const daysBack = params.days_back ?? 7;
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - daysBack);

    let query = supabase
      .from('inventory_events')
      .select('*')
      .gte('created_at', startDate.toISOString())
      .order('created_at', { ascending: false });

    if (params.product_name) {
      query = query.ilike('name', `%${params.product_name}%`);
    }
    if (params.category) {
      query = query.eq('category', params.category);
    }
    if (params.event_type) {
      query = query.eq('event_type', params.event_type);
    }

    const { data, error } = await query.limit(50);
    if (error) {
      return { success: false, error: error.message, message: 'Failed to query inventory' };
    }

    const stockLevels = new Map<string, number>();
    (data ?? []).forEach((event: Record<string, unknown>) => {
      const eventData = event as Record<string, unknown> & {
        name?: string;
        product_name?: string;
        metadata?: { product_name?: string };
        unit?: string;
        quantity?: number;
        event_type?: string;
      };
      const productName = (eventData.name ?? eventData.product_name ?? eventData?.metadata?.product_name) ?? 'Unknown';
      const key = `${productName}_${eventData.unit}`;
      const current = stockLevels.get(key) ?? 0;
      if (eventData.event_type === 'receiving' || eventData.event_type === 'physical_count') {
        stockLevels.set(key, current + Number(eventData.quantity ?? 0));
      } else if (eventData.event_type === 'sale' || eventData.event_type === 'disposal') {
        stockLevels.set(key, current - Number(eventData.quantity ?? 0));
      }
    });

    const stockSummary = Array.from(stockLevels.entries()).map(([key, quantity]) => {
      const [product_name, unit] = key.split('_');
      return { product_name, unit, current_stock: quantity };
    });

    return {
      success: true,
      data: {
        recent_events: data ?? [],
        stock_summary: stockSummary,
        query_params: params,
        days_back: daysBack,
      },
      message: `Found ${(data ?? []).length} events in the last ${daysBack} days`,
    };

  } catch (error) {
    console.error('Error querying inventory:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      message: 'Failed to query inventory'
    };
  }
}

export async function getProductInfo(params: { product_name: string }): Promise<{ success: boolean; data?: unknown; message?: string; error?: string }> {
  try {
    // Get recent events for this product (table uses 'name')
    const { data: events, error } = await supabase
      .from('inventory_events')
      .select('*')
      .ilike('name', `%${params.product_name}%`)
      .order('created_at', { ascending: false })
      .limit(10);

    if (error) {
      return {
        success: false,
        error: error.message,
        message: `Failed to get information for ${params.product_name}`
      };
    }

    // Calculate current stock
    let currentStock = 0;
    let lastUnit = 'lbs';
    let category = 'finfish';

    events?.forEach(event => {
      if (event.event_type === 'receiving' || event.event_type === 'physical_count') {
        currentStock += event.quantity;
      } else if (event.event_type === 'sale' || event.event_type === 'disposal') {
        currentStock -= event.quantity;
      }
      lastUnit = event.unit;
      category = event.category;
    });

    // Get product specifications based on category
    const specifications = getProductSpecifications(params.product_name, category);

    return {
      success: true,
      data: {
        product_name: params.product_name,
        category,
        current_stock: currentStock,
        unit: lastUnit,
        recent_events: events,
        specifications,
        last_updated: events?.[0]?.created_at
      },
      message: `Current stock: ${currentStock} ${lastUnit} of ${params.product_name}`
    };

  } catch (error) {
    console.error('Error getting product info:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      message: `Failed to get product information`
    };
  }
}

export async function getHACCPGuidance(params: {
  product_category: string;
  processing_method?: string
}): Promise<{ success: boolean; data?: unknown; message?: string; error?: string }> {
  try {
    const guidance = getHACCPRequirements(params.product_category, params.processing_method);
    
    return {
      success: true,
      data: guidance,
      message: `HACCP guidance for ${params.product_category}${params.processing_method ? ` (${params.processing_method})` : ''}`
    };

  } catch (error) {
    console.error('Error getting HACCP guidance:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      message: 'Failed to get HACCP guidance'
    };
  }
}

export async function getFreezerTemperatures(params: { unit?: 'F' | 'C'; name_contains?: string }): Promise<{ success: boolean; data?: unknown; message?: string; error?: string }> {
  try {
    // Prefer Edge Function based on DB readings (less dependency on upstream API)
    try {
      const { data: fnRes, error: fnErr } = await (supabase as { functions: { invoke: (name: string, options: { body: unknown }) => Promise<{ data: unknown; error: unknown }> } }).functions.invoke('freezer_temperatures', {
        body: params,
      });
      if (!fnErr && fnRes) {
        return fnRes as { success: boolean; data?: unknown; message?: string; error?: string };
      }
      if (fnErr) {
        console.warn('freezer_temperatures function error, falling back to TempStick API:', fnErr);
      }
    } catch (fxErr) {
      console.warn('freezer_temperatures invoke failed; using TempStick API fallback', fxErr);
    }

    // Fallback: use TempStick API via service
    const preferF = (params?.unit ?? 'F').toUpperCase() === 'F';
    const nameFilter = (params?.name_contains ?? '').trim().toLowerCase();
    let sensors: Record<string, unknown>[] = [];
    try {
      sensors = await tempStickService.getAllSensors();
    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      console.warn('TempStick sensors unavailable, continuing without live readings:', message);
      return {
        success: true,
        data: { count: 0, sensors: [] },
        message: 'TempStick sensors unavailable — skipping live temperature data',
      };
    }
    const results = sensors
      .filter((s: Record<string, unknown>) => {
        const sensorData = s as Record<string, unknown> & {
          sensor_name?: string;
          name?: string;
          last_temp?: number;
        };
        const name = String(sensorData.sensor_name ?? sensorData.name ?? '').toLowerCase();
        // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
        const isNameFreezer = name.includes('freezer') || name.includes('walk in freezer') || name.includes('reach in freezer');
        const matchesFilter = nameFilter ? name.includes(nameFilter) : true;
        const isLikelyFreezer = Number.isFinite(sensorData.last_temp) && (sensorData.last_temp ?? 0) < -5;
        // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
        return matchesFilter && (isNameFreezer || isLikelyFreezer);
      })
      .map((s: Record<string, unknown>) => {
        const c = Number(s.last_temp);
        const f = Number.isFinite(c) ? c * 1.8 + 32 : null;
        const value = preferF ? f : c;
        return {
          sensor_id: s.sensor_id,
          name: s.sensor_name ?? s.name,
          temperature: value,
          unit: preferF ? 'F' : 'C',
          humidity: Number.isFinite(s.last_humidity) ? Number(s.last_humidity) : null,
          status: s.status,
          last_reading: s.last_reading,
          battery_level: s.battery_level,
          signal_strength: s.rssi,
        };
      })
      .sort((a: Record<string, unknown>, b: Record<string, unknown>) => String(a.name).localeCompare(String(b.name)));

    return { success: true, data: { count: results.length, sensors: results }, message: results.length > 0 ? `Found ${results.length} freezer sensors` : 'No freezer sensors found' };
  } catch (error) {
    console.error('Error getting freezer temperatures:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      message: 'Failed to get freezer temperatures'
    };
  }
}

/**
 * Helper functions
 */

function getProductSpecifications(_productName: string, category: string) {
  const specs: Record<string, unknown> = {
    category,
    common_units: ['lbs', 'kg', 'cases'],
    shelf_life: '5-7 days fresh',
    storage_temp: '32-38°F'
  };

  // Add category-specific specs
  switch (category) {
    case 'finfish':
      specs.processing_methods = ['fresh', 'frozen', 'iqf', 'h&g', 'fillets'];
      specs.quality_grades = ['premium', 'grade_a', 'sashimi_grade'];
      break;
    case 'shellfish':
      specs.processing_methods = ['live', 'shucked', 'frozen'];
      specs.quality_grades = ['premium', 'select', 'standard'];
      specs.storage_temp = '32-35°F';
      break;
    case 'crustaceans':
      specs.processing_methods = ['live', 'cooked', 'frozen', 'picked_meat'];
      specs.quality_grades = ['jumbo', 'colossal', 'premium'];
      break;
  }

  return specs;
}

function getHACCPRequirements(category: string, processingMethod?: string) {
  const baseRequirements = {
    category,
    processing_method: processingMethod,
    critical_control_points: [] as string[],
    temperature_requirements: {} as Record<string, string>,
    monitoring_frequency: 'Every 4 hours',
    corrective_actions: [] as string[]
  };

  switch (category) {
    case 'finfish':
      baseRequirements.temperature_requirements = {
        receiving: '32-38°F',
        storage: '32-38°F',
        display: '32-38°F'
      };
      baseRequirements.critical_control_points = [
        'Receiving temperature check',
        'Cold storage maintenance',
        'Time/temperature during processing'
      ];
      break;
    
    case 'shellfish':
      baseRequirements.temperature_requirements = {
        receiving: '45°F or below',
        storage: '32-35°F',
        live_storage: '32-35°F with proper ventilation'
      };
      baseRequirements.critical_control_points = [
        'Shellfish tag verification',
        'Temperature control',
        'Live shellfish handling'
      ];
      break;
    
    case 'crustaceans':
      baseRequirements.temperature_requirements = {
        live_receiving: '38-45°F',
        cooked_receiving: '32-38°F',
        storage: '32-38°F'
      };
      baseRequirements.critical_control_points = [
        'Live animal welfare',
        'Cooking temperature verification',
        'Post-cook cooling'
      ];
      break;
  }

  baseRequirements.corrective_actions = [
    'Reject product if temperature exceeds limits',
    'Immediate use or disposal of compromised product',
    'Equipment calibration and repair',
    'Staff retraining on procedures'
  ];

  return baseRequirements;
}

/**
 * Main tool executor function for OpenAI Realtime API
 */
export async function executeSeafoodTool(toolName: string, parameters: Record<string, unknown>): Promise<{ success: boolean; data?: unknown; message?: string; error?: string }> {
  console.log(`Executing seafood tool: ${toolName}`, parameters);

  switch (toolName) {
    case 'create_inventory_event':
      return await createInventoryEvent(parameters as unknown as InventoryEvent);

    case 'query_inventory':
      return await queryInventory(parameters as unknown as InventoryQuery & { days_back?: number });

    case 'get_product_info':
      return await getProductInfo(parameters as unknown as { product_name: string });

    case 'get_haccp_guidance':
      return await getHACCPGuidance(parameters as unknown as { product_category: string; processing_method?: string });

    case 'get_freezer_temperatures':
      return await getFreezerTemperatures(parameters as unknown as { unit?: 'F' | 'C'; name_contains?: string });

    default:
      return {
        success: false,
        message: `Unknown tool: ${toolName}`
      };
  }
}

/**
 * Official SDK Tool Execution Functions
 * These functions are called by the new tool() definitions
 */

// Execute inventory query for the official SDK tool
export async function executeInventoryQuery(params: {
  product_name?: string | null;
  category?: string | null;
  event_type?: string | null;
  date_range?: { start: string; end: string } | null;
}): Promise<{ success: boolean; data?: unknown; message?: string; error?: string }> {
  // Convert nullable values to undefined for compatibility
  const cleanParams = {
    product_name: params.product_name ?? undefined,
    category: params.category ?? undefined,
    event_type: params.event_type ?? undefined,
    date_range: params.date_range ?? undefined,
  };
  return await queryInventory(cleanParams);
}

// Execute inventory event creation for the official SDK tool
export async function executeInventoryEvent(params: {
  event_type: string;
  product_name: string;
  category: string;
  quantity: number;
  unit: string;
  unit_price?: number | null;
  unit_cost?: number | null;
  total_amount?: number | null;
  vendor?: string | null;
  customer?: string | null;
  notes?: string | null;
  temperature?: number | null;
  condition?: string | null;
  lot_number?: string | null;
  expiration_date?: string | null;
}): Promise<{ success: boolean; data?: unknown; message?: string; error?: string }> {
  // Convert to InventoryEvent type, converting null to undefined
  const inventoryEvent: InventoryEvent = {
    event_type: params.event_type as 'receiving' | 'sale' | 'disposal' | 'physical_count',
    product_name: params.product_name,
    category: params.category,
    quantity: params.quantity,
    unit: params.unit,
    unit_price: params.unit_price ?? undefined,
    unit_cost: params.unit_cost ?? undefined,
    total_amount: params.total_amount ?? undefined,
    vendor: params.vendor ?? undefined,
    customer: params.customer ?? undefined,
    notes: params.notes ?? undefined,
    temperature: params.temperature ?? undefined,
    condition: params.condition ?? undefined,
    lot_number: params.lot_number ?? undefined,
    expiration_date: params.expiration_date ?? undefined,
  };
  return await createInventoryEvent(inventoryEvent);
}

// Execute inventory update for the official SDK tool
export async function executeInventoryUpdate(params: {
  event_id: string;
  updates: Record<string, unknown>;
}): Promise<{ success: boolean; data?: unknown; message?: string; error?: string }> {
  try {
    const { data, error } = await supabase
      .from('inventory_events')
      .update(params.updates)
      .eq('id', params.event_id)
      .select()
      .single();

    if (error) {
      return {
        success: false,
        message: `Failed to update inventory event: ${error.message}`
      };
    }

    return {
      success: true,
      data,
      message: `Successfully updated inventory event ${params.event_id}`
    };
  } catch (error) {
    return {
      success: false,
      message: `Error updating inventory event: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}

// Execute temperature query for the official SDK tool
export async function executeTemperatureQuery(params: {
  sensor_id?: string | null;
  location?: string | null;
}): Promise<{ success: boolean; data?: unknown; message?: string; error?: string }> {
  // Convert to the expected parameter format for getFreezerTemperatures
  const tempParams = {
    unit: 'F' as const,
    name_contains: (params.location ?? params.sensor_id) ?? undefined
  };
  return await getFreezerTemperatures(tempParams);
}
