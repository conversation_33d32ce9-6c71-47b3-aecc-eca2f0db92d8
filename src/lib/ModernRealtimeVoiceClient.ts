import {
  OpenAIRealtimeWebRTC,
  OpenAIRealtimeWebSocket,
  RealtimeAgent,
  RealtimeSession,
} from '@openai/agents/realtime';
import type { RealtimeSessionOptions } from '@openai/agents/realtime';
import { tool } from '@openai/agents';
import { z } from 'zod';
import {
  executeSeafoodTool,
  queryInventoryTool,
  addInventoryTool,
  updateInventoryTool,
  getTemperatureTool
} from './realtime-tools';

/**
 * Modern 2025 OpenAI Agents SDK Voice Client for Seafood Inventory Management
 * Uses the official @openai/agents package with RealtimeAgent and RealtimeSession
 */

export interface ModernVoiceClientConfig {
  apiKey?: string;
  ephemeralToken?: string;
  relayUrl?: string;
  transport?: 'webrtc' | 'websocket';
  model?: string;
  voice?: string;
  enableDebugLogs?: boolean;
  useInsecureApiKey?: boolean;
}

export interface ModernVoiceClientEvents {
  onConnected: () => void;
  onDisconnected: () => void;
  onListening: () => void;
  onProcessing: () => void;
  onTranscript: (transcript: string, isFinal: boolean) => void;
  onResponse: (text: string) => void;
  onError: (error: string) => void;
  onToolCall: (toolName: string, args: Record<string, unknown>, result: unknown) => void;
}

// Tools are now imported from realtime-tools.ts using the official SDK patterns

export class ModernRealtimeVoiceClient {
  private agent: RealtimeAgent;
  private session: RealtimeSession | null = null;
  private config: ModernVoiceClientConfig;
  private events: Partial<ModernVoiceClientEvents> = {};
  private isConnected = false;
  private transport: OpenAIRealtimeWebRTC | OpenAIRealtimeWebSocket;
  private audioContext: AudioContext | null = null;
  private audioQueueTime = 0;
  private micStream: MediaStream | null = null;
  private micAudioContext: AudioContext | null = null;
  private micSource: MediaStreamAudioSourceNode | null = null;
  private micProcessor: ScriptProcessorNode | null = null;
  private micGainNode: GainNode | null = null;
  private userSpeaking = false;
  private lastVoiceActivity = 0;
  private readonly silenceTimeoutMs = 800;
  private readonly voiceActivityThreshold = 0.015;

  constructor(config: ModernVoiceClientConfig, events: Partial<ModernVoiceClientEvents> = {}) {
    this.config = config;
    this.events = events;

    // Create the RealtimeAgent with seafood-specific configuration
    this.agent = new RealtimeAgent({
      name: 'SeafoodInventoryAssistant',
      instructions: `You are a professional seafood inventory assistant for Pacific Cloud Seafoods. Your primary role is to help process voice commands for inventory management with extremely high accuracy for seafood industry terminology.

CORE EXPERTISE:
- 200+ seafood species recognition including scientific names, common names, and regional aliases
- Seafood processing methods: Fresh, Frozen, IQF, Live, Smoked, Cured, H&G, Fillets
- Quality grades: Premium, Grade A, Sashimi Grade, Select, Restaurant Quality
- Market forms: Whole, Fillets, H&G, Portions, Steaks, Loins, Clusters, Picked meat
- Industry units: lbs, kg, cases, dozens, pieces, each, bags
- Vendor/supplier recognition for major seafood companies
- HACCP compliance terms and temperature requirements

PROCESSING PRIORITIES:
1. Species identification with confidence scoring
2. Quantity and unit extraction with conversions
3. Event type classification (receiving, sale, disposal, physical count)
4. Vendor/customer identification
5. Quality and condition assessment
6. HACCP data extraction (temperature, dates, conditions)

RESPONSE GUIDELINES:
- Always confirm what you understood from the voice input
- Use function calls immediately when you have enough information
- Ask for clarification if any critical details are missing
- Provide helpful suggestions for common seafood industry tasks
- Maintain professional tone while being conversational
- Always prioritize food safety and HACCP compliance`,

      tools: [
        queryInventoryTool,
        addInventoryTool,
        updateInventoryTool,
        getTemperatureTool,
      ],
    });

    // Configure WebRTC transport explicitly for better debugging
    const targetModel = this.config.model ?? 'gpt-realtime';
    const useWebSocket = this.config.transport === 'websocket' || Boolean(this.config.relayUrl);

    // Expose model for fetch patch to ensure ?model=... is always present when rewriting
    if (typeof window !== 'undefined') {
      try {
        (window as unknown as Record<string, unknown>).__openai_realtime_model = targetModel;
      } catch {}
    }

    if (useWebSocket) {
      this.transport = new OpenAIRealtimeWebSocket({
        model: targetModel,
        useInsecureApiKey: this.config.useInsecureApiKey ?? true,
      });
    } else {
      // Correct WebRTC configuration: include model so SDK calls /v1/realtime/calls?model=...
      this.transport = new OpenAIRealtimeWebRTC({
        model: targetModel,
        useInsecureApiKey: this.config.useInsecureApiKey ?? true,
        // SDK handles media stream setup automatically
      });
    }

    // Simplified session configuration following official guide patterns
    const sessionOptions: Partial<RealtimeSessionOptions> = {
      transport: this.transport,
      model: targetModel,
    };

    this.session = new RealtimeSession(this.agent, sessionOptions);
    this.setupEventListeners();

    if (this.config.enableDebugLogs) {
      console.log('🎤 Modern RealtimeVoiceClient initialized with OpenAI Agents SDK');
    }
  }

  async connect(): Promise<boolean> {
    try {
      if (this.isConnected) {
        if (this.config.enableDebugLogs) {
          console.log('⚠️ Already connected, disconnecting first...');
        }
        await this.disconnect();
      }

      // For WebRTC, the SDK handles audio automatically
      // For WebSocket, we need to prepare audio context
      if (typeof window !== 'undefined' && this.config.transport === 'websocket') {
        await this.ensureAudioContext();
      }

      if (this.config.enableDebugLogs) {
        console.log('🔌 Connecting to OpenAI Realtime API...');
      }

      if (!this.session) {
        throw new Error('Realtime session not initialized');
      }

      this.patchRealtimeFetchHeader();

      const isRelay = Boolean(this.config.relayUrl);
      const apiKey =
        this.config.ephemeralToken
          ?? this.config.apiKey
          ?? (isRelay ? 'relay' : undefined);

      if (this.config.enableDebugLogs) {
        console.log('🔑 Using API key:', {
          hasEphemeralToken: !!this.config.ephemeralToken,
          ephemeralTokenPrefix: this.config.ephemeralToken?.slice(0, 8),
          hasApiKey: !!this.config.apiKey,
          isRelay,
          finalApiKey: apiKey?.slice(0, 8)
        });
      }

      if (!apiKey) {
        throw new Error('Missing API credentials for realtime session');
      }

      // Use the simple connect pattern from OpenAI documentation
      if (isRelay) {
        await this.session.connect({
          apiKey,
          url: this.config.relayUrl,
        });
        // For WebSocket relay, we need to handle microphone manually
        await this.startMicrophoneCapture();
      } else {
        // For WebRTC, the SDK handles audio automatically
        await this.session.connect({ apiKey });
      }

      this.isConnected = true;
      this.events.onConnected?.();

      if (this.config.enableDebugLogs) {
        console.log('✅ Connected to OpenAI Realtime API with modern SDK');
      }

      return true;
    } catch (error) {
      console.error('❌ Connection failed:', error);
      this.events.onError?.(`Connection failed: ${error instanceof Error ? error.message : error}`);
      return false;
    }
  }

  private setupEventListeners(): void {
    if (!this.session) return;

    // Official RealtimeSession event handlers following the guide patterns
    this.session.on('audio', (audioEvent: any) => {
      void this.handleAudioEvent(audioEvent);
    });

    // History updates for conversation management
    this.session.on('history_updated', (history: any) => {
      if (this.config.enableDebugLogs) {
        console.log('📚 History updated:', history.length, 'items');
      }
      // Extract transcript from history for compatibility
      const lastItem = history[history.length - 1];
      if (lastItem?.type === 'message') {
        this.events.onTranscript?.(lastItem.content || '', true);
      }
    });

    // Audio interruption handling
    this.session.on('audio_interrupted', () => {
      if (this.config.enableDebugLogs) {
        console.log('🛑 Audio interrupted');
      }
      if (this.audioContext) {
        this.audioQueueTime = this.audioContext.currentTime;
      }
    });

    // Tool approval requests (if using tools with needsApproval: true)
    this.session.on('tool_approval_requested', (_context: any, _agent: any, request: any) => {
      if (this.config.enableDebugLogs) {
        console.log('🔧 Tool approval requested:', request);
      }
      // Auto-approve for now - in production you might want to show UI
      this.session?.approve(request.approvalItem);
    });

    // Guardrail events
    this.session.on('guardrail_tripped', (event: any) => {
      if (this.config.enableDebugLogs) {
        console.log('🚨 Guardrail tripped:', event);
      }
      this.events.onError?.(`Guardrail violation: ${event.details?.name || 'Unknown'}`);
    });

    // Connection state changes
    this.session.on('connected', () => {
      this.isConnected = true;
      if (this.config.enableDebugLogs) {
        console.log('🔌 Connected to OpenAI Realtime API');
      }
    });

    this.session.on('disconnected', () => {
      this.isConnected = false;
      this.events.onDisconnected?.();
      if (this.config.enableDebugLogs) {
        console.log('🔌 Disconnected from OpenAI Realtime API');
      }
    });

    // Error handling
    this.session.on('error', (error: any) => {
      console.error('❌ Session error:', error);
      this.events.onError?.(error instanceof Error ? error.message : String(error));
    });
  }

  private async handleAudioEvent(event: unknown): Promise<void> {
    if (typeof window === 'undefined') return;

    try {
      const buffer = this.extractAudioBuffer(event);
      if (!buffer) {
        if (this.config.enableDebugLogs) {
          console.warn('🎧 Ignoring audio event without buffer payload');
        }
        return;
      }

      const audioContext = await this.ensureAudioContext();
      if (!audioContext) return;

      this.scheduleAudioPlayback(audioContext, buffer);
      this.events.onProcessing?.();
    } catch (error) {
      console.error('Failed to handle realtime audio event:', error);
      this.events.onError?.('Failed to play audio output');
    }
  }

  private extractAudioBuffer(event: unknown): ArrayBuffer | null {
    if (!event) return null;

    const maybeWithData = event as { data?: ArrayBuffer | ArrayLike<number> };
    if (maybeWithData?.data instanceof ArrayBuffer) {
      return maybeWithData.data;
    }

    if (ArrayBuffer.isView(maybeWithData?.data)) {
      const view = maybeWithData?.data as ArrayBufferView;
      return view.buffer.slice(view.byteOffset, view.byteOffset + view.byteLength);
    }

    if (event instanceof ArrayBuffer) {
      return event;
    }

    return null;
  }

  private async ensureAudioContext(): Promise<AudioContext | null> {
    if (typeof window === 'undefined') return null;
    if (!this.audioContext) {
      try {
        this.audioContext = new AudioContext({ sampleRate: 24000 });
      } catch {
        // Fallback to default constructor in case the sample rate is unsupported
        this.audioContext = new AudioContext();
      }
      this.audioQueueTime = this.audioContext.currentTime;
    }

    if (this.audioContext.state === 'suspended') {
      try {
        await this.audioContext.resume();
      } catch (error) {
        console.warn('Unable to resume AudioContext:', error);
        return null;
      }
    }

    return this.audioContext;
  }

  private scheduleAudioPlayback(audioContext: AudioContext, buffer: ArrayBuffer): void {
    if (buffer.byteLength === 0) return;

    const floatData = this.decodePcmToFloat(buffer);
    if (!floatData) return;

    const sampleRate = 24000;
    const audioBuffer = audioContext.createBuffer(1, floatData.length, sampleRate);
    audioBuffer.copyToChannel(floatData, 0);

    const source = audioContext.createBufferSource();
    source.buffer = audioBuffer;
    source.connect(audioContext.destination);

    const startAt = Math.max(this.audioQueueTime, audioContext.currentTime + 0.05);
    source.start(startAt);
    this.audioQueueTime = startAt + audioBuffer.duration;

    source.addEventListener('ended', () => {
      if (this.audioContext && this.audioQueueTime <= this.audioContext.currentTime + 0.05) {
        this.audioQueueTime = this.audioContext.currentTime;
      }
    });
  }

  private decodePcmToFloat(buffer: ArrayBuffer): Float32Array | null {
    if (buffer.byteLength % 2 !== 0) {
      console.warn('Unexpected audio buffer length, ignoring chunk');
      return null;
    }

    const view = new DataView(buffer);
    const totalSamples = buffer.byteLength / 2;
    const float32 = new Float32Array(totalSamples);

    for (let i = 0; i < totalSamples; i += 1) {
      const sample = view.getInt16(i * 2, true);
      float32[i] = sample / 0x8000;
    }

    return float32;
  }

  async disconnect(): Promise<void> {
    try {
      if (this.session) {
        this.session.close();
      }
      this.isConnected = false;
      this.events.onDisconnected?.();
      this.stopMicrophoneCapture();
      if (this.audioContext) {
        await this.audioContext.close().catch(() => undefined);
        this.audioContext = null;
        this.audioQueueTime = 0;
      }
    } catch (error) {
      console.error('Error during disconnect:', error);
    }
  }

  // The new SDK handles microphone automatically, but we can provide status methods
  getConnectionStatus(): boolean {
    return this.isConnected;
  }

  // Send a text message (useful for testing or mixed input)
  async sendMessage(message: string): Promise<void> {
    if (!this.session || !this.isConnected) {
      throw new Error('Not connected to session');
    }
    
    await this.session.sendMessage(message);
  }

  // Official RealtimeSession conversation history management
  clearConversationHistory(): void {
    if (!this.session) {
      console.warn('Cannot clear history: session not initialized');
      return;
    }

    try {
      this.session.updateHistory([]);
      if (this.config.enableDebugLogs) {
        console.log('🗑️ Conversation history cleared');
      }
    } catch (error) {
      console.error('Failed to clear conversation history:', error);
    }
  }

  // Get current conversation history from the session
  getConversationHistory(): unknown[] {
    if (!this.session) {
      return [];
    }

    try {
      return this.session.history || [];
    } catch (error) {
      console.error('Failed to get conversation history:', error);
      return [];
    }
  }

  // Update configuration
  updateConfig(newConfig: Partial<ModernVoiceClientConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * Ensure WebRTC offer/answer requests include the required beta header for the GA API.
   */
  private patchRealtimeFetchHeader(): void {
    if (typeof window === 'undefined') return;
    const marker = '__openai_realtime_ga_patched__';
    if ((window as unknown as Record<string, unknown>)[marker]) return;

    const originalFetch: typeof window.fetch = window.fetch.bind(window) as typeof window.fetch;
    (window as unknown as Record<string, unknown>)[marker] = true;

    const patchedFetch: typeof window.fetch = async (input: RequestInfo | URL, init?: RequestInit) => {
      try {
        let urlStr: string | undefined;
        if (typeof input === 'string') {
          urlStr = input;
        } else if (input instanceof URL) {
          urlStr = input.toString();
        } else if (typeof Request !== 'undefined' && input instanceof Request) {
          urlStr = input.url;
        }

        if (urlStr && urlStr.includes('/v1/realtime')) {
          const baseHeaders = new Headers(
            init?.headers ?? (typeof Request !== 'undefined' && input instanceof Request ? input.headers : undefined)
          );

          // Ensure correct headers for SDP negotiation endpoint
          if (urlStr.includes('/v1/realtime/calls')) {
            baseHeaders.set('Accept', 'application/sdp');
            // For POST offer->answer exchange, Content-Type must be application/sdp
            const method = (init?.method ?? (typeof Request !== 'undefined' && input instanceof Request ? input.method : 'GET')).toUpperCase();
            if (method === 'POST') {
              baseHeaders.set('Content-Type', 'application/sdp');
            }
          }

          const performFetch = async (requestInput: RequestInfo | URL, requestInit?: RequestInit) => {
            const response = await originalFetch(requestInput, requestInit);
            if (!response.ok) {
              try {
                const clone = response.clone();
                const text = await clone.text();
                console.warn(`Realtime fetch failure: status=${response.status} body=${text.slice(0, 200)}`);
              } catch (err) {
                console.warn(`Realtime fetch failure: status=${response.status} (no body) error=${String(err)}`);
              }
            }
            return response;
          };

          // Rewrite OpenAI calls negotiation to local proxy to avoid CORS
          let finalUrlStr: string = urlStr ?? '';
          try {
            const abs = new URL(urlStr, window.location.origin);
            const isCalls = abs.pathname === '/v1/realtime/calls';
            const isOpenAI = abs.host.includes('api.openai.com');
            if (isCalls && isOpenAI) {
              let search = abs.search;
              if (!search || search.length === 0) {
                const modelOverride = (typeof window !== 'undefined'
                  ? (window as unknown as Record<string, unknown>).__openai_realtime_model as string | undefined
                  : undefined);
                if (modelOverride) {
                  search = `?model=${encodeURIComponent(modelOverride)}`;
                }
              }
              finalUrlStr = `/api/openai/realtime/calls${search || ''}`;
            } else {
              finalUrlStr = abs.toString();
            }
          } catch {
            // If URL parsing fails, keep original string
          }

          const isRequest = typeof Request !== 'undefined' && input instanceof Request;
          if (isRequest) {
            const req = input as Request;
            const method = req.method.toUpperCase();
            let body: BodyInit | undefined;
            if (method !== 'GET' && method !== 'HEAD') {
              try {
                // Clone to preserve original request and read as text (works for SDP and JSON)
                body = await req.clone().text();
              } catch {
                body = undefined;
              }
            }
            const updatedRequest = new Request(finalUrlStr, { method, headers: baseHeaders, body });
            return performFetch(updatedRequest);
          }

          return performFetch(finalUrlStr, { ...init, headers: baseHeaders });
        }
      } catch (error) {
        console.warn('Realtime fetch patch fallback:', error);
      }

      return originalFetch(input as RequestInfo, init);
    };

    (window as unknown as Record<string, unknown>).fetch = patchedFetch as unknown as typeof window.fetch;
  }

  private async startMicrophoneCapture(): Promise<void> {
    if (typeof window === 'undefined') return;
    if (this.micStream || !navigator.mediaDevices?.getUserMedia) {
      return;
    }

    try {
      this.micStream = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: 24000,
          channelCount: 1,
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
        },
      });

      this.micAudioContext = new AudioContext({ sampleRate: 24000 });
      this.micSource = this.micAudioContext.createMediaStreamSource(this.micStream);
      this.micProcessor = this.micAudioContext.createScriptProcessor(2048, 1, 1);
      this.micGainNode = this.micAudioContext.createGain();
      this.micGainNode.gain.value = 0;

      this.micProcessor.onaudioprocess = (event) => {
        const channelData = event.inputBuffer.getChannelData(0);
        this.processMicrophoneFrame(channelData);
      };

      this.micSource.connect(this.micProcessor);
      this.micProcessor.connect(this.micGainNode);
      this.micGainNode.connect(this.micAudioContext.destination);

      if (this.config.enableDebugLogs) {
        console.log('🎙️ Microphone capture started for realtime session');
      }
    } catch (error) {
      console.error('Failed to access microphone:', error);
      this.events.onError?.('Microphone permission denied or unavailable');
    }
  }

  private stopMicrophoneCapture(): void {
    this.userSpeaking = false;
    this.lastVoiceActivity = 0;

    if (this.micProcessor) {
      this.micProcessor.disconnect();
      this.micProcessor.onaudioprocess = null;
      this.micProcessor = null;
    }

    if (this.micSource) {
      this.micSource.disconnect();
      this.micSource = null;
    }

    if (this.micGainNode) {
      this.micGainNode.disconnect();
      this.micGainNode = null;
    }

    if (this.micAudioContext) {
      void this.micAudioContext.close().catch(() => undefined);
      this.micAudioContext = null;
    }

    if (this.micStream) {
      this.micStream.getTracks().forEach((track) => track.stop());
      this.micStream = null;
    }
  }

  private processMicrophoneFrame(channelData: Float32Array): void {
    if (!this.session || !this.isConnected) {
      return;
    }

    const pcm = this.floatToPcm16(channelData);
    if (!pcm) return;

    try {
      this.session.sendAudio(pcm.buffer);
    } catch (error) {
      console.error('Failed to stream audio to realtime session:', error);
      return;
    }

    const rms = this.calculateRms(channelData);
    const now = performance.now();

    if (rms >= this.voiceActivityThreshold) {
      this.lastVoiceActivity = now;
      if (!this.userSpeaking) {
        this.userSpeaking = true;
        this.events.onListening?.();
      }
    } else if (this.userSpeaking && now - this.lastVoiceActivity > this.silenceTimeoutMs) {
      this.userSpeaking = false;
      try {
        this.session.sendAudio(new ArrayBuffer(0), { commit: true });
      } catch (error) {
        console.warn('Failed to commit audio buffer:', error);
      }
      this.events.onProcessing?.();
    }
  }

  private floatToPcm16(channelData: Float32Array): Int16Array | null {
    if (!channelData || channelData.length === 0) {
      return null;
    }

    const pcm = new Int16Array(channelData.length);
    for (let i = 0; i < channelData.length; i += 1) {
      const sample = Math.max(-1, Math.min(1, channelData[i]));
      pcm[i] = sample < 0 ? sample * 0x8000 : sample * 0x7fff;
    }
    return pcm;
  }

  private calculateRms(data: Float32Array): number {
    if (!data || data.length === 0) return 0;
    let sumSquares = 0;
    for (let i = 0; i < data.length; i += 1) {
      const sample = data[i];
      sumSquares += sample * sample;
    }
    return Math.sqrt(sumSquares / data.length);
  }
}

// Factory function for easy creation
export function createModernRealtimeVoiceClient(
  config: ModernVoiceClientConfig,
  events?: Partial<ModernVoiceClientEvents>
): ModernRealtimeVoiceClient {
  return new ModernRealtimeVoiceClient(config, events);
}
