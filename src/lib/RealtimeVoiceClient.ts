import { RealtimeClient } from '@openai/realtime-api-beta';
import { SEAFOOD_INVENTORY_TOOLS, executeSeafoodTool } from './realtime-tools';

/**
 * Modern OpenAI Realtime Voice Client for Seafood Inventory Management
 * Uses the official @openai/realtime-api-beta SDK with proper WebRTC integration
 */

export interface VoiceClientConfig {
  apiKey?: string;
  relayUrl?: string;
  enableDebugLogs?: boolean;
  voice?: 'alloy' | 'echo' | 'shimmer';
  temperature?: number;
  transport?: 'ws' | 'webrtc';
  // Advanced voice mode options
  autoContinue?: boolean;             // auto resume listening after speaking
  silenceThreshold?: number;          // RMS threshold for silence detection
  silenceDurationMs?: number;         // duration of silence to end turn
  // OpenAI Best Practice: Audio optimization settings
  audioFormat?: 'pcm16' | 'g711_ulaw' | 'g711_alaw';
  sampleRate?: number;
  enableTranscription?: boolean;
  maxContextTokens?: number;
  // Performance monitoring
  enableLatencyTracking?: boolean;
  enableQualityMetrics?: boolean;
}

export interface VoiceClientEvents {
  onConnected: () => void;
  onDisconnected: () => void;
  onListening: () => void;
  onProcessing: () => void;
  onTranscript: (transcript: string, isFinal: boolean) => void;
  onResponse: (text: string) => void;
  onError: (error: string) => void;
  onToolCall: (toolName: string, args: Record<string, unknown>, result: unknown) => void;
  onLatencyUpdate: (latencyMs: number) => void;
}

export class RealtimeVoiceClient {
  private client: RealtimeClient;
  private isConnected = false;
  private isListening = false;
  private events: Partial<VoiceClientEvents> = {};
  private config: VoiceClientConfig;
  private startTime = 0;
  private audioContext: AudioContext | null = null;
  private pc: RTCPeerConnection | null = null; // for WebRTC transport
  private remoteAudioEl: HTMLAudioElement | null = null;
  private dc: RTCDataChannel | null = null;
  private pendingToolCalls: Map<string, { call_id: string; name: string; args: string }> = new Map();
  // Microphone capture resources
  private micAudioContext: AudioContext | null = null;
  private micStream: MediaStream | null = null;
  private micTrack: MediaStreamTrack | null = null;
  private micSourceNode: MediaStreamAudioSourceNode | null = null;
  private micProcessorNode: ScriptProcessorNode | null = null; // legacy fallback
  private micWorkletNode: AudioWorkletNode | null = null;
  private audioQueue: Int16Array[] = [];
  private isPlaying = false;
  private nextPlayTime = 0;
  private silenceTimer: number | null = null;
  private vadFallbackTimer: number | null = null;
  private lastAudioAt: number | null = null;
  private isSpeaking = false;
  // OpenAI Best Practice: Track conversation state for truncation
  private currentResponseId: string | null = null;
  private audioStartTime: number | null = null;
  private lastAudioDeltaAt: number | null = null;
  private lastAudioDoneAt: number | null = null;
  // Performance monitoring
  private requestStartTime: number | null = null;
  private performanceMetrics: {
    totalRequests: number;
    averageLatency: number;
    connectionUptime: number;
    errorCount: number;
  } = {
    totalRequests: 0,
    averageLatency: 0,
    connectionUptime: 0,
    errorCount: 0
  };
  private hasPendingAudio = false;
  
  private sendRtcEvent(type: string, payload: Record<string, unknown> = {}): void {
    if (this.dc && this.dc.readyState === 'open') {
      try {
        this.dc.send(JSON.stringify({ type, ...payload }));
      } catch (e) {
        console.error('RTC send failed', e);
      }
    }
  }

  /**
   * Handle PCM16 audio chunks directly (from conversation.updated delta.audio)
   */
  private handleAudioPCM16(pcm16: Int16Array): void {
    try {
      if (!this.audioStartTime && !this.isSpeaking) {
        this.audioStartTime = Date.now();
        this.isSpeaking = true;

        if (this.config.enableLatencyTracking && this.requestStartTime) {
          const latency = this.audioStartTime - this.requestStartTime;
          this.updateLatencyMetrics(latency);
          this.events.onLatencyUpdate?.(latency);
          this.requestStartTime = null;
        }
      }

      // On first audio chunk, mute mic to prevent echo
      if (!this.lastAudioDeltaAt) {
        if (this.micTrack?.enabled) {
          try { this.micTrack.enabled = false; } catch (error) {
            console.warn('Error disabling mic track:', error);
          }
        }
      }

      this.lastAudioDeltaAt = Date.now();
      this.audioQueue.push(pcm16);
      this.playAudioQueue();
    } catch (error) {
      console.error('Error handling PCM16 audio delta:', error);
    }
  }

  constructor(config: VoiceClientConfig, events: Partial<VoiceClientEvents> = {}) {
    this.config = config;
    this.events = events;
    // Initialize transport
    if (config.transport === 'webrtc') {
      // WebRTC path uses ephemeral session; RealtimeClient instance is not used
      // We still create a client instance to reuse event wiring where possible
      this.client = new RealtimeClient({ apiKey: 'webrtc', dangerouslyAllowAPIKeyInBrowser: true });
    } else {
      // WebSocket path (relay or direct)
      if (config.relayUrl) {
        // Convert relative URL to full WebSocket URL
        const wsUrl = config.relayUrl.startsWith('ws://') || config.relayUrl.startsWith('wss://') 
          ? config.relayUrl 
          : `ws://${window.location.host}${config.relayUrl}`;
        
        if (config.enableDebugLogs) {
          console.log(`🔌 Using WebSocket relay URL: ${wsUrl}`);
        }
        
        this.client = new RealtimeClient({ url: wsUrl });
      } else if (config.apiKey) {
        this.client = new RealtimeClient({ apiKey: config.apiKey, dangerouslyAllowAPIKeyInBrowser: true });
      } else {
        throw new Error('Either apiKey, relayUrl or transport=webrtc must be provided');
      }
      this.setupEventHandlers();
      this.configureSession();
    }
  }

  private setupEventHandlers(): void {
    // Connection events
    this.client.on('error', (event) => {
      console.error('RealtimeClient error:', event);
      this.events.onError?.(event.message ?? 'Connection error');
    });

    // Audio response events for playing assistant's voice
    this.client.on('response.audio.delta', (event: { delta: string }) => {
      if (this.config.enableDebugLogs) {
        console.log('🔊 Received audio delta chunk');
      }
      // On first audio chunk, ensure mic is muted to prevent echo interrupting
      if (!this.audioStartTime) {
        if (this.micTrack?.enabled) {
          try { this.micTrack.enabled = false; } catch (error) {
            console.warn('Error disabling mic track:', error);
          }
        }
      }
      // Track last output activity for interrupt guard
      this.lastAudioDeltaAt = Date.now();
      if (event.delta) {
        this.handleAudioDelta(event.delta);
      }
    });

    this.client.on('response.audio.done', async () => {
      if (this.config.enableDebugLogs) {
        console.log('🔊 Audio response complete');
      }
      this.lastAudioDoneAt = Date.now();
      // Ensure all audio is played before changing state
      await this.flushAudioQueue();

      // OpenAI Best Practice: Reset tracking variables when response completes
      this.currentResponseId = null;
      this.audioStartTime = null;

      // Auto-continue: after speaking, go back to listening
      if (this.config.autoContinue !== false) {
        this.isSpeaking = false;
        // Delay listening re-entry long enough to avoid tail-cut
        const guardDelay = Math.max(600, this.config.silenceDurationMs ?? 800);
        setTimeout(() => {
          if (this.isConnected && !this.isListening) {
            this.startListening();
          }
        }, guardDelay);
      }
    });

    this.client.on('response.audio_transcript.delta', (event: { delta: string }) => {
      if (this.config.enableDebugLogs) {
        console.log('📝 Audio transcript delta:', event.delta);
      }
      // This provides the text transcript of what's being spoken
      if (event.delta) {
        this.events.onResponse?.(event.delta);
      }
    });

    this.client.on('response.audio_transcript.done', (event: { transcript: string }) => {
      if (this.config.enableDebugLogs) {
        console.log('📝 Audio transcript complete:', event.transcript);
      }
    });

    // Conversation events
    this.client.on('conversation.interrupted', () => {
      if (this.config.enableDebugLogs) {
        console.log('Conversation interrupted (user started speaking)');
      }

      // Guard against spurious interrupts immediately after output
      const now = Date.now();
      const guardWindow = Math.max(600, this.config.silenceDurationMs ?? 800);
      if ((this.lastAudioDeltaAt && now - this.lastAudioDeltaAt < guardWindow) ||
          (this.lastAudioDoneAt && now - this.lastAudioDoneAt < guardWindow)) {
        if (this.config.enableDebugLogs) {
          console.log('🛡️ Interrupt ignored within guard window');
        }
        return;
      }

      // Only truncate if we are actively listening (mic enabled). Prevents
      // output from being cut by echo/noise when not listening.
      if (this.isListening && this.isSpeaking && this.currentResponseId && this.audioStartTime) {
        const audioPlayedDuration = Date.now() - this.audioStartTime;
        this.truncateCurrentResponse(audioPlayedDuration);
      }

      // Do not force UI into listening here; startListening controls that
      this.isSpeaking = false;
    });

    this.client.on('conversation.updated', ({ item, delta }: { item: Record<string, unknown>; delta: Record<string, unknown> }) => {
      if (this.config.enableDebugLogs) {
        console.log('Conversation updated:', { item, delta });
      }

      // Handle different item types
      switch (item.type) {
        case 'message':
          if (item.role === 'user' && delta?.transcript) {
            // User speech transcription
            const isFinal = item.status === 'completed';
            this.events.onTranscript?.(delta.transcript, isFinal);
            
            if (isFinal) {
              this.events.onProcessing?.();
            }
          } else if (item.role === 'assistant' && delta?.transcript) {
            // Assistant response
            this.events.onResponse?.(delta.transcript);
          }
          break;

        case 'function_call':
          if (item.status === 'completed') {
            // Function call completed, execute it
            this.handleFunctionCall(item);
          }
          break;
      }

      // GA path: process audio/transcript deltas via conversation.updated as well
      if (delta) {
        // Audio stream chunks (Int16Array) — push to playback queue
        if (delta.audio && (delta.audio instanceof Int16Array)) {
          this.handleAudioPCM16(delta.audio);
        }
        // Some client builds may pass audio as ArrayBuffer
        else if (delta.audio && (delta.audio as { buffer: ArrayBuffer }).buffer instanceof ArrayBuffer) {
          try {
            const view = new Int16Array((delta.audio as ArrayBuffer));
            this.handleAudioPCM16(view);
          } catch (error) {
            console.warn('Error handling audio ArrayBuffer:', error);
          }
        }

        // Text transcript deltas
        if (delta.transcript && typeof delta.transcript === 'string') {
          this.events.onResponse?.(delta.transcript);
        }
      }

      // Calculate and report latency
      if (item.role === 'assistant' && item.status === 'completed') {
        const latency = Date.now() - this.startTime;
        this.events.onLatencyUpdate?.(latency);
      }
    });

    this.client.on('conversation.item.appended', ({ item }) => {
      if (this.config.enableDebugLogs) {
        console.log('Item appended:', item);
      }
    });

    this.client.on('conversation.item.completed', ({ item }) => {
      if (this.config.enableDebugLogs) {
        console.log('Item completed:', item);
      }

      // OpenAI Best Practice: Track response ID for truncation
      if (item.type === 'message' && item.role === 'assistant') {
        this.currentResponseId = item.id;
      }
    });

    // Raw event logging for debugging
    if (this.config.enableDebugLogs) {
      this.client.on('realtime.event', ({ time, source, event }) => {
        console.log(`[${time}] ${source}:`, event);
      });
    
    // Additional audio-specific event handlers for debugging
    this.client.on('input_audio_buffer.speech_started', (event) => {
      if (this.config.enableDebugLogs) {
        console.log('🎤 Speech started detected!', event);
      }
    });

    this.client.on('input_audio_buffer.speech_stopped', (event) => {
      if (this.config.enableDebugLogs) {
        console.log('🔇 Speech stopped detected!', event);
      }

      // OpenAI Best Practice: Track request start time for latency measurement
      if (this.config.enableLatencyTracking) {
        this.requestStartTime = Date.now();
        this.performanceMetrics.totalRequests++;
      }

      this.events.onProcessing?.();
    });

    this.client.on('conversation.item.input_audio_transcription.completed', (event) => {
      if (this.config.enableDebugLogs) {
        console.log('📝 Transcription completed:', event);
      }
      
      // Trigger response generation after transcription is complete
      // This ensures the AI responds to user input
      if (this.config.enableDebugLogs) {
        console.log('🤖 Triggering response generation...');
      }
      this.client.createResponse();
    });

    this.client.on('conversation.item.input_audio_transcription.failed', (event) => {
      if (this.config.enableDebugLogs) {
        console.error('❌ Transcription failed:', event);
      }
    });
    }
  }

  private configureSession(): void {
    // Configure session with seafood-specific settings
    this.client.updateSession({
      modalities: ['text', 'audio'],  // Enable both text and audio output
      instructions: `You are a professional seafood inventory assistant for Pacific Cloud Seafoods. Your primary role is to help process voice commands for inventory management with extremely high accuracy for seafood industry terminology.

CORE EXPERTISE:
- 200+ seafood species recognition including scientific names, common names, and regional aliases
- Seafood processing methods: Fresh, Frozen, IQF, Live, Smoked, Cured, H&G, Fillets
- Quality grades: Premium, Grade A, Sashimi Grade, Select, Restaurant Quality
- Market forms: Whole, Fillets, H&G, Portions, Steaks, Loins, Clusters, Picked meat
- Industry units: lbs, kg, cases, dozens, pieces, each, bags
- Vendor/supplier recognition for major seafood companies
- HACCP compliance terms and temperature requirements

PROCESSING PRIORITIES:
1. Species identification with confidence scoring
2. Quantity and unit extraction with conversions
3. Event type classification (receiving, sale, disposal, physical count)
4. Vendor/customer identification
5. Quality and condition assessment
6. HACCP data extraction (temperature, dates, conditions)

RESPONSE GUIDELINES:
- Always confirm what you understood from the voice input
- Use function calls immediately when you have enough information
- Ask for clarification if any critical details are missing
- Provide brief, professional responses focused on seafood operations
- Include relevant HACCP or safety information when appropriate

TARGET RESPONSE TIME: <300ms total processing time

CONFIDENCE SCORING:
- High (>90%): Clear species, quantity, and event type - proceed with function call
- Medium (70-89%): Some ambiguity in one parameter - ask for clarification
- Low (<70%): Multiple unclear parameters - request user to repeat

Always prioritize accuracy for seafood species - incorrect species identification can cause significant business issues.`,
      voice: (this.config.voice as 'alloy' | 'echo' | 'shimmer') ?? 'alloy',
      // OpenAI Best Practice: Optimized audio configuration
      input_audio_format: this.config.audioFormat ?? 'pcm16',
      output_audio_format: this.config.audioFormat ?? 'pcm16',
      input_audio_transcription: this.config.enableTranscription !== false ? { model: 'whisper-1' } : null,
      turn_detection: {
        type: 'server_vad',
        threshold: 0.5,
        prefix_padding_ms: 300,
        // Default to a slightly longer silence to avoid cutting tails
        silence_duration_ms: this.config.silenceDurationMs ?? 800,
      },
      temperature: this.config.temperature ?? 0.3,
      // Prefer voice (audio) responses while still allowing text deltas
      tool_choice: 'auto',
      max_response_output_tokens: 4096,
    });

    // Add seafood inventory tools
    SEAFOOD_INVENTORY_TOOLS.forEach(tool => {
      this.client.addTool(tool, async (args: Record<string, unknown>) => {
        return await executeSeafoodTool(tool.name, args);
      });
    });
  }

  private async handleFunctionCall(item: { call: { name: string; arguments: Record<string, unknown> } }): Promise<void> {
    try {
      const { name, arguments: args } = item.call;
      
      if (this.config.enableDebugLogs) {
        console.log(`Executing function: ${name}`, args);
      }

      const result = await executeSeafoodTool(name, args);
      
      this.events.onToolCall?.(name, args, result);

      // The RealtimeClient automatically sends the result back to the model
      // via the tool callback mechanism, so we don't need to manually send it

    } catch (error) {
      console.error('Function call error:', error);
      this.events.onError?.(`Function call failed: ${error}`);
    }
  }

  /**
   * Handle audio delta chunks from the assistant's response
   */
  private handleAudioDelta(base64Audio: string): void {
    try {
      // OpenAI Best Practice: Track response start for truncation and latency
      if (!this.audioStartTime && !this.isSpeaking) {
        this.audioStartTime = Date.now();
        this.isSpeaking = true;

        // Calculate and track latency
        if (this.config.enableLatencyTracking && this.requestStartTime) {
          const latency = this.audioStartTime - this.requestStartTime;
          this.updateLatencyMetrics(latency);
          this.events.onLatencyUpdate?.(latency);
          this.requestStartTime = null;
        }
      }

      // Decode base64 to binary
      const binaryString = atob(base64Audio);
      const bytes = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }
      
      // Convert to Int16Array (PCM16 format)
      const pcm16 = new Int16Array(bytes.buffer);
      
      // Add to queue and play
      this.audioQueue.push(pcm16);
      this.playAudioQueue();
    } catch (error) {
      console.error('Error handling audio delta:', error);
    }
  }

  /**
   * Play queued audio chunks
   */
  private async playAudioQueue(): Promise<void> {
    // Initialize audio context on first use with nullish coalescing
    this.audioContext ??= new ((window as Window & { webkitAudioContext?: typeof AudioContext }).AudioContext ?? (window as Window & { webkitAudioContext?: typeof AudioContext }).webkitAudioContext ?? AudioContext)({
      sampleRate: 24000 // Match OpenAI's output sample rate
    });

    // Resume audio context if suspended (browser security)
    if (this.audioContext && this.audioContext.state === 'suspended') {
      await this.audioContext.resume();
    }

    // Process queue if not already playing
    if (!this.isPlaying && this.audioQueue.length > 0) {
      this.isPlaying = true;
      
      while (this.audioQueue.length > 0) {
        const pcm16Data = this.audioQueue.shift();
        if (pcm16Data) {
          await this.playPCM16Audio(pcm16Data);
        }
      }
      
      this.isPlaying = false;
    }
  }

  /**
   * Play a single PCM16 audio chunk
   */
  private async playPCM16Audio(pcm16Data: Int16Array): Promise<void> {
    if (!this.audioContext) return;

    // Convert PCM16 to Float32 for Web Audio API
    const float32Data = new Float32Array(pcm16Data.length);
    for (let i = 0; i < pcm16Data.length; i++) {
      float32Data[i] = pcm16Data[i] / 32768.0; // Convert to -1.0 to 1.0 range
    }

    // Create audio buffer
    const audioBuffer = this.audioContext.createBuffer(
      1, // mono
      float32Data.length,
      24000 // sample rate
    );
    audioBuffer.getChannelData(0).set(float32Data);

    // Create and configure source
    const source = this.audioContext.createBufferSource();
    source.buffer = audioBuffer;
    source.connect(this.audioContext.destination);

    // Schedule playback
    const currentTime = this.audioContext.currentTime;
    const startTime = Math.max(currentTime, this.nextPlayTime);
    source.start(startTime);
    
    // Update next play time for seamless playback
    this.nextPlayTime = startTime + audioBuffer.duration;

    // Wait for playback to complete
    return new Promise<void>((resolve) => {
      source.onended = () => resolve();
    });
  }

  /**
   * Flush any remaining audio in the queue
   */
  private async flushAudioQueue(): Promise<void> {
    if (this.audioQueue.length > 0) {
      await this.playAudioQueue();
    }
  }

  /**
   * Connect to OpenAI Realtime API with proper state management
   */
  async connect(): Promise<boolean> {
    try {
      if (this.config.transport === 'webrtc') {
        const ok = await this.connectWebRTC();
        if (ok) return true;
        // Fallback to WS if configured
        if (!this.config.relayUrl && !this.config.apiKey) {
          // If WebRTC fails and no fallback is possible, return false
          return false;
        }
        if (this.config.enableDebugLogs) {
          console.warn('WebRTC connection failed; falling back to WebSocket transport');
        }
        // continue into WS branch
      }
      // Step 0: Check if already connected
      if (this.isConnected) {
        if (this.config.enableDebugLogs) {
          console.log('⚠️ Already connected, disconnecting first...');
        }
        await this.disconnect();
        // Give a brief moment for cleanup
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      if (this.config.enableDebugLogs) {
        console.log('🎤 Starting connection process...');
      }

      // Step 1: Probe microphone permission (actual streaming starts in startListening)
      try {
        const test = await navigator.mediaDevices.getUserMedia({ audio: true });
        test.getTracks().forEach((t) => t.stop());
        if (this.config.enableDebugLogs) {
          console.log('🎤 Microphone available');
        }
      } catch (micError) {
        console.error('❌ Microphone access denied:', micError);
        this.events.onError?.('Microphone access required for voice input. Please check browser permissions.');
        return false;
      }

      // Step 2: Connect to realtime server/API
      try {
        if (this.config.enableDebugLogs) {
          console.log('🔌 Connecting to OpenAI Realtime API...');
        }
        await this.client.connect();
        
        // Wait for connection to be fully established
        await new Promise(resolve => setTimeout(resolve, 200));
        
        if (this.config.enableDebugLogs) {
          console.log('✅ WebSocket connection established');
        }
      } catch (wsError) {
        console.error('❌ Realtime connection failed:', wsError);
        // Nothing to cleanup from probe
        this.events.onError?.(`Could not connect to realtime server: ${wsError instanceof Error ? wsError.message : wsError}`);
        return false;
      }

      // Step 3: Initialize audio context (microphone is handled by WebRTC)
      try {
        if (this.config.enableDebugLogs) {
          console.log('🎙️ Initializing audio context...');
        }
        // The microphone connection is handled automatically by the RealtimeClient
        // via WebRTC after the WebSocket connection is established
        
        if (this.config.enableDebugLogs) {
          console.log('✅ Audio context ready');
        }
      } catch (attachError) {
        console.error('❌ Failed to attach microphone:', attachError);
        // Clean up: disconnect from API and release microphone
        try {
          this.client.disconnect();
        } catch (cleanupError) {
          console.error('Error during cleanup:', cleanupError);
        }
        // Nothing to cleanup from probe
        this.events.onError?.(`Failed to attach microphone to realtime session: ${attachError instanceof Error ? attachError.message : attachError}`);
        return false;
      }

      // Step 4: Initialize and unlock audio context for playback
      try {
        if (!this.audioContext) {
          this.audioContext = new ((window as Window & { webkitAudioContext?: typeof AudioContext }).AudioContext ?? (window as Window & { webkitAudioContext?: typeof AudioContext }).webkitAudioContext ?? AudioContext)({
            sampleRate: 24000
          });
          if (this.config.enableDebugLogs) {
            console.log('🔊 Audio context initialized for playback');
          }
        }
        if (this.audioContext.state === 'suspended') {
          await this.audioContext.resume();
          if (this.config.enableDebugLogs) {
            console.log('🔊 Audio context resumed');
          }
        }
      } catch (audioError) {
        console.error('Failed to initialize audio context:', audioError);
        // Non-fatal - continue without audio playback
      }

      // Step 5: Mark as connected and notify
      this.isConnected = true;

      if (this.config.enableDebugLogs) {
        console.log('✅ Connected to OpenAI Realtime API with microphone and audio playback');
      }

      this.events.onConnected?.();
      return true;
      
    } catch (error) {
      console.error('Unexpected error during connection:', error);
      // Ensure cleanup on any unexpected error
      try {
        await this.disconnect();
      } catch (cleanupError) {
        console.error('Error during error cleanup:', cleanupError);
      }
      this.events.onError?.(`Connection failed: ${error instanceof Error ? error.message : error}`);
      return false;
    }
  }

  /**
   * Connect via WebRTC using server-minted ephemeral token
   */
  private async connectWebRTC(): Promise<boolean> {
    try {
      // Probe mic permissions early
      try {
        const test = await navigator.mediaDevices.getUserMedia({ audio: true });
        test.getTracks().forEach((t) => t.stop());
      } catch {
        this.events.onError?.('Microphone access required. Please grant permission.');
        return false;
      }

      // Create RTCPeerConnection
      const pc = new RTCPeerConnection({
        iceServers: [{ urls: 'stun:stun.l.google.com:19302' }],
      });
      this.pc = pc;

      // Outbound mic stream
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          channelCount: 1,
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
        },
      });
      stream.getTracks().forEach((track) => pc.addTrack(track, stream));

      // Inbound audio playback
      pc.ontrack = (e) => {
        const [remote] = e.streams;
        if (!this.remoteAudioEl) {
          this.remoteAudioEl = new Audio();
          this.remoteAudioEl.autoplay = true;
          // playsInline is only typed on HTMLVideoElement; set as attribute for audio element
          try { this.remoteAudioEl.setAttribute('playsinline', 'true'); } catch { /* noop */ }
          this.remoteAudioEl.muted = false;
          try {
            if (!document.body.contains(this.remoteAudioEl)) {
              this.remoteAudioEl.style.display = 'none';
              document.body.appendChild(this.remoteAudioEl);
            }
          } catch (appendErr) {
            if (this.config.enableDebugLogs) {
              console.warn('Failed to append remote audio element to DOM:', appendErr);
            }
          }
        }
        this.remoteAudioEl.srcObject = remote;
        void this.remoteAudioEl.play().catch((err) => {
          if (this.config.enableDebugLogs) {
            console.warn('Remote audio autoplay prevented; will require user interaction.', err);
          }
        });
      };

      // Data channel (optional diagnostics)
      const dc = pc.createDataChannel('oai-events');
      this.dc = dc;
      dc.onopen = () => {
        if (this.config.enableDebugLogs) console.log('RTC datachannel open');
        // Send session configuration and tool definitions
        this.sendRtcEvent('session.update', {
          session: {
            modalities: ['text', 'audio'],
            voice: (this.config.voice as string) ?? 'alloy',
            tool_choice: 'auto',
            input_audio_transcription: this.config.enableTranscription !== false ? { model: 'whisper-1' } : null,
            turn_detection: {
              type: 'server_vad',
              threshold: 0.5,
              prefix_padding_ms: 300,
              silence_duration_ms: this.config.silenceDurationMs ?? 800,
            },
            tools: SEAFOOD_INVENTORY_TOOLS,
          },
        });
      };
      dc.onmessage = async (ev) => {
        try {
          const raw = ev.data;
          if (typeof raw !== 'string') return;
          const evt = JSON.parse(raw);
          const type: string = evt?.type ?? '';
          if (this.config.enableDebugLogs && type) console.log('RTC event', type, evt);

          // Normalize server prefix
          const normType = type.startsWith('server.') ? type.slice('server.'.length) : type;

          if (normType === 'conversation.item.created' && evt.item?.type === 'function_call') {
            const item = evt.item;
            this.pendingToolCalls.set(item.id, {
              call_id: item.call_id,
              name: item.name,
              args: '',
            });
          } else if (normType === 'response.function_call_arguments.delta') {
            const itemId = evt.item_id;
            const delta = evt.delta;
            const rec = itemId ? this.pendingToolCalls.get(itemId) : undefined;
            if (rec && typeof delta === 'string') {
              rec.args += delta;
              this.pendingToolCalls.set(itemId, rec);
            }
          } else if (normType === 'response.output_item.done') {
            // When the output item is done, execute the tool if we have one
            const itemId = evt.item?.id ?? evt.item_id;
            const rec = itemId ? this.pendingToolCalls.get(itemId) : undefined;
            if (rec) {
              try {
                const parsedArgs = rec.args ? JSON.parse(rec.args) : {};
                const result = await executeSeafoodTool(rec.name, parsedArgs);
                // Send function_call_output back
                this.sendRtcEvent('conversation.item.create', {
                  item: {
                    type: 'function_call_output',
                    call_id: rec.call_id,
                    output: JSON.stringify(result),
                  },
                });
                // Continue the response
                this.sendRtcEvent('response.create');
                // Surface to app
                this.events.onToolCall?.(rec.name, parsedArgs, result);
              } catch (e: unknown) {
                this.sendRtcEvent('conversation.item.create', {
                  item: {
                    type: 'function_call_output',
                    call_id: rec.call_id,
                    output: JSON.stringify({ success: false, error: e?.message ?? String(e) }),
                  },
                });
                this.sendRtcEvent('response.create');
              } finally {
                this.pendingToolCalls.delete(itemId);
              }
            }
          }
        } catch (e) {
          if (this.config.enableDebugLogs) console.warn('RTC onmessage parse error', e);
        }
      };

      // Create offer
      const offer = await pc.createOffer({ offerToReceiveAudio: true });
      await pc.setLocalDescription(offer);

      // Mint ephemeral token with correct model
      const model = process.env.REALTIME_MODEL ?? import.meta.env.VITE_REALTIME_MODEL ?? 'gpt-4o-realtime-preview-2024-12-17';
      const tokenRes = await fetch(`/api/voice/ephemeral-token?voice=${encodeURIComponent(this.config.voice ?? 'alloy')}&model=${encodeURIComponent(model)}`);
      if (!tokenRes.ok) {
        const errText = await tokenRes.text().catch(() => '');
        throw new Error(`Ephemeral token HTTP ${tokenRes.status}${errText ? `: ${errText}` : ''}`);
      }
      const tokenJson = await tokenRes.json();
      const ephemeral = tokenJson?.client_secret?.value ?? tokenJson?.client_secret ?? tokenJson?.value;
      if (!ephemeral) {
        this.events.onError?.('Failed to obtain ephemeral token');
        return false;
      }

      // Send SDP offer to OpenAI and get answer
      const realtimeUrl = `https://api.openai.com/v1/realtime/calls?model=${encodeURIComponent(model)}`;
      const sdpRes = await fetch(realtimeUrl, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${ephemeral}`,
          'Content-Type': 'application/sdp',
          // In GA, server replies with SDP when Accept is explicitly set
          Accept: 'application/sdp',
        },
        body: offer.sdp ?? '',
      });

      // If the response is not OK, surface a helpful error message
      if (!sdpRes.ok) {
        const text = await sdpRes.text().catch(() => '');
        let message = text;
        try {
          const j = JSON.parse(text);
          message = j?.error?.message || j?.message || text;
        } catch {
          // non-JSON body, keep raw text
        }
        throw new Error(`Realtime SDP exchange failed (${sdpRes.status}): ${message?.slice(0, 300)}`);
      }

      const answer = await sdpRes.text();
      const trimmed = (answer || '').trim();
      // Basic guard: WebRTC SDP must start with "v=" line
      if (!trimmed.startsWith('v=')) {
        // Log first bytes to help diagnose (likely a JSON error if Accept header missing)
        throw new Error(
          `Invalid SDP answer received from server. First bytes: ${trimmed.slice(0, 120)}`
        );
      }

      await pc.setRemoteDescription({ type: 'answer', sdp: trimmed });

      pc.onconnectionstatechange = () => {
        if (pc.connectionState === 'connected') {
          this.isConnected = true;
          this.events.onConnected?.();
        } else if (pc.connectionState === 'failed' || pc.connectionState === 'disconnected' || pc.connectionState === 'closed') {
          this.isConnected = false;
          this.events.onDisconnected?.();
        }
      };

      return true;
    } catch (err) {
      console.error('WebRTC connect error:', err);
      this.events.onError?.(err instanceof Error ? err.message : 'WebRTC connection failed');
      return false;
    }
  }

  /**
   * Disconnect from OpenAI Realtime API with proper cleanup
   */
  async disconnect(): Promise<void> {
    try {
      if (this.config.enableDebugLogs) {
        console.log('🔌 Disconnecting from OpenAI Realtime API...');
      }

      // Stop listening first to clean up any ongoing audio processing
      if (this.isListening) {
        this.stopListening();
      }

      // WebRTC cleanup
      if (this.pc) {
        try { this.pc.ontrack = null; } catch (error) {
          console.warn('Error clearing pc.ontrack:', error);
        }
        try { this.pc.close(); } catch (error) {
          console.warn('Error closing peer connection:', error);
        }
        this.pc = null;
      }
      if (this.remoteAudioEl) {
        try { (this.remoteAudioEl.srcObject as MediaStream | null)?.getTracks().forEach(t => t.stop()); } catch (error) {
          console.warn('Error stopping remote audio tracks:', error);
        }
        this.remoteAudioEl.srcObject = null;
        try {
          if (this.remoteAudioEl.parentNode) {
            this.remoteAudioEl.parentNode.removeChild(this.remoteAudioEl);
          }
        } catch (removeErr) {
          if (this.config.enableDebugLogs) {
            console.warn('Error removing remote audio element from DOM:', removeErr);
          }
        }
        this.remoteAudioEl = null;
      }

      // Clean up audio context
      if (this.audioContext) {
        try {
          await this.audioContext.close();
          this.audioContext = null;
          this.audioQueue = [];
          this.isPlaying = false;
          this.nextPlayTime = 0;
          if (this.config.enableDebugLogs) {
            console.log('🔊 Audio context closed');
          }
        } catch (audioError) {
          console.error('Error closing audio context:', audioError);
        }
      }

      if (this.isConnected) {
        // Step 1: Clean up microphone connection
        try {
          // The RealtimeClient handles microphone cleanup automatically
          if (this.config.enableDebugLogs) {
            console.log('🎤 Cleaning up microphone resources');
          }
        } catch (micError) {
          console.error('Error cleaning up microphone:', micError);
        }

        // Explicitly release browser microphone resources
        try {
          if (this.micProcessorNode) {
            this.micProcessorNode.disconnect();
            this.micProcessorNode.onaudioprocess = null;
            this.micProcessorNode = null;
          }
          if (this.micSourceNode) {
            this.micSourceNode.disconnect();
            this.micSourceNode = null;
          }
          if (this.micAudioContext) {
            await this.micAudioContext.close();
            this.micAudioContext = null;
          }
          if (this.micStream) {
            this.micStream.getTracks().forEach((t) => t.stop());
            this.micStream = null;
          }
        } catch (micCleanupError) {
          console.error('Error releasing microphone resources:', micCleanupError);
        }
        
        // Step 2: Disconnect from OpenAI API
        try {
          this.client.disconnect();
          if (this.config.enableDebugLogs) {
            console.log('🔌 WebSocket disconnected');
          }
        } catch (wsError) {
          console.error('Error disconnecting WebSocket:', wsError);
        }
        
        // Step 3: Clean up state
        this.isConnected = false;
        this.isListening = false;
        
        // Step 4: Notify listeners
        this.events.onDisconnected?.();
        
        if (this.config.enableDebugLogs) {
          console.log('✅ Disconnected from OpenAI Realtime API');
        }
      } else {
        if (this.config.enableDebugLogs) {
          console.log('ℹ️ Not connected, skipping disconnect');
        }
      }
    } catch (error) {
      console.error('Error during disconnect:', error);
      // Force clean state even if errors occurred
      this.isConnected = false;
      this.isListening = false;
    }
  }

  /**
   * Start listening for voice input with connection validation
   */
  async startListening(): Promise<void> {
    if (!this.isConnected) {
      const errorMsg = 'Not connected to OpenAI Realtime API. Please connect first.';
      this.events.onError?.(errorMsg);
      if (this.config.enableDebugLogs) {
        console.error('❌ Cannot start listening:', errorMsg);
      }
      return;
    }

    if (this.isListening) {
      if (this.config.enableDebugLogs) {
        console.log('ℹ️ Already listening, no action needed');
      }
      return;
    }

    try {
      this.isListening = true;
      this.startTime = Date.now();

      if (this.config.enableDebugLogs) {
        console.log('🎤 Starting microphone capture for realtime streaming...');
      }

      // Ensure playback audio context is unlocked during user interaction
      this.audioContext ??= new ((window as Window & { webkitAudioContext?: typeof AudioContext }).AudioContext ?? (window as Window & { webkitAudioContext?: typeof AudioContext }).webkitAudioContext ?? AudioContext)({
        sampleRate: 24000,
      });
      if (this.audioContext.state === 'suspended') {
        try {
          await this.audioContext.resume();
        } catch (resumeErr) {
          if (this.config.enableDebugLogs) {
            console.warn('AudioContext resume blocked until explicit user interaction', resumeErr);
          }
        }
      }

      // Acquire mic stream if needed
      if (!this.micStream) {
        this.micStream = await navigator.mediaDevices.getUserMedia({
          audio: {
            echoCancellation: true,
            noiseSuppression: true,
            autoGainControl: true,
            channelCount: 1,
          },
        });
        const tracks = this.micStream.getAudioTracks();
        this.micTrack = tracks && tracks.length > 0 ? tracks[0] : null;
      }

      // Ensure mic track enabled when listening
      if (this.micTrack) {
        try { this.micTrack.enabled = true; } catch (error) {
          console.warn('Error enabling mic track:', error);
        }
      }

      // Initialize capture context
      if (!this.micAudioContext) {
        this.micAudioContext = new ((window as Window & { webkitAudioContext?: typeof AudioContext }).AudioContext ?? (window as Window & { webkitAudioContext?: typeof AudioContext }).webkitAudioContext ?? AudioContext)();
      } else if (this.micAudioContext.state === 'suspended') {
        await this.micAudioContext.resume();
      }

      // Set up processing nodes using AudioWorkletNode (preferred)
      this.micSourceNode = this.micAudioContext.createMediaStreamSource(this.micStream);
      try {
        await this.micAudioContext.audioWorklet.addModule(new URL('./audio/mic-processor.worklet.js', import.meta.url));
        this.micWorkletNode = new AudioWorkletNode(this.micAudioContext, 'mic-processor', { numberOfInputs: 1, numberOfOutputs: 0, channelCount: 1 });
        this.micSourceNode.connect(this.micWorkletNode);

        const targetRate = 24000;
        const sourceRate = this.micAudioContext.sampleRate;
        this.micWorkletNode.port.onmessage = (evt: MessageEvent) => {
          if (!this.isListening) return;
          if (!this.isConnected) return; // avoid appending when connection failed
          const buf: Float32Array = evt.data?.samples;
          if (!buf) return;

          // Update last audio time for VAD fallback
          this.lastAudioAt = Date.now();

          // Resample if needed
          let float32Data: Float32Array;
          if (sourceRate === targetRate) {
            float32Data = buf;
          } else {
            const ratio = sourceRate / targetRate;
            const outLen = Math.max(1, Math.floor(buf.length / ratio));
            float32Data = new Float32Array(outLen);
            for (let i = 0; i < outLen; i++) {
              float32Data[i] = buf[Math.floor(i * ratio)] ?? 0;
            }
          }

          // Convert to PCM16
          const pcm = new Int16Array(float32Data.length);
          for (let i = 0; i < float32Data.length; i++) {
            const s = Math.max(-1, Math.min(1, float32Data[i]));
            pcm[i] = s < 0 ? s * 0x8000 : s * 0x7fff;
          }

          try {
            if (this.isConnected) {
              this.client.appendInputAudio(pcm);
              this.hasPendingAudio = true;
            }
          } catch (e) {
            if (this.config.enableDebugLogs) console.warn('appendInputAudio failed (likely disconnected)', e);
          }

          // Silence detection
          const rms = Math.sqrt(float32Data.reduce((s, v) => s + v * v, 0) / float32Data.length);
          const threshold = this.config.silenceThreshold ?? 0.015;
          const durationMs = this.config.silenceDurationMs ?? 700;
          if (rms < threshold) {
            this.silenceTimer ??= window.setTimeout(() => {
                this.silenceTimer = null;
                if (!this.isListening) return;
                if (this.config.enableDebugLogs) console.log('🤫 Silence detected — creating response');
                this.stopListening();
              }, durationMs);
          } else if (this.silenceTimer != null) {
            clearTimeout(this.silenceTimer);
            this.silenceTimer = null;
          }
        };
      } catch (err) {
        // Fallback to ScriptProcessorNode if AudioWorklet is unavailable
        console.warn('AudioWorklet unavailable, falling back to ScriptProcessorNode:', err);
        this.micProcessorNode = this.micAudioContext.createScriptProcessor(4096, 1, 1);
        this.micSourceNode.connect(this.micProcessorNode);
        this.micProcessorNode.connect(this.micAudioContext.destination);
        const sourceRate = this.micAudioContext.sampleRate;
        const targetRate = 24000;
        this.micProcessorNode.onaudioprocess = (evt: AudioProcessingEvent) => {
          if (!this.isListening) return;
          if (!this.isConnected) return;
          const input = evt.inputBuffer.getChannelData(0);
          this.lastAudioAt = Date.now();
          let float32Data: Float32Array;
          if (sourceRate === targetRate) {
            float32Data = new Float32Array(input);
          } else {
            const ratio = sourceRate / targetRate;
            const outLen = Math.floor(input.length / ratio);
            float32Data = new Float32Array(outLen);
            for (let i = 0; i < outLen; i++) float32Data[i] = input[Math.floor(i * ratio)] ?? 0;
          }
          const pcm = new Int16Array(float32Data.length);
          for (let i = 0; i < float32Data.length; i++) {
            const s = Math.max(-1, Math.min(1, float32Data[i]));
            pcm[i] = s < 0 ? s * 0x8000 : s * 0x7fff;
          }
          try { this.client.appendInputAudio(pcm); this.hasPendingAudio = true; } catch (error) {
            console.warn('Error appending input audio:', error);
          }
          const rms = Math.sqrt(float32Data.reduce((s, v) => s + v * v, 0) / float32Data.length);
          const threshold = this.config.silenceThreshold ?? 0.015;
          const durationMs = this.config.silenceDurationMs ?? 700;
          if (rms < threshold) {
            this.silenceTimer ??= window.setTimeout(() => { this.silenceTimer = null; if (!this.isListening) return; this.stopListening(); }, durationMs);
          } else if (this.silenceTimer != null) { clearTimeout(this.silenceTimer); this.silenceTimer = null; }
        };
      }

      // VAD timeout fallback — guarantee a response even if VAD misses
      this.lastAudioAt = Date.now();
      const fallbackWindow = (this.config.silenceDurationMs ?? 700) + 600; // grace period
      this.vadFallbackTimer = window.setInterval(() => {
        if (!this.isListening) return;
        if (this.lastAudioAt && Date.now() - this.lastAudioAt > fallbackWindow) {
          if (this.config.enableDebugLogs) console.log('⏱️ VAD timeout fallback — committing and creating response');
          this.stopListening();
        }
      }, 250);

      this.events.onListening?.();

      if (this.config.enableDebugLogs) {
        console.log('🎤 Voice capture active (PCM16 @24kHz). Speak now.');
      }

    } catch (error) {
      console.error('❌ Failed to start listening:', error);
      this.isListening = false;
      this.events.onError?.(`Failed to start listening: ${error instanceof Error ? error.message : error}`);
    }
  }

  /**
   * Stop listening for voice input
   */
  stopListening(): void {
    if (!this.isListening) {
      if (this.config.enableDebugLogs) {
        console.log('ℹ️ Not listening, no action needed');
      }
      return;
    }

    try {
      this.isListening = false;

      // If we captured audio, commit input buffer and ask the model for a response now
      try {
        if (this.hasPendingAudio) {
          if (this.config.enableDebugLogs) {
            console.log('🧠 Committing input buffer and requesting model response (audio + text)...');
          }
          // Explicitly signal end of input buffer to the Realtime API
          try {
            (this.client as { realtime?: { send?: (type: string, data: Record<string, unknown>) => void } }).realtime?.send?.('input_audio_buffer.commit', {});
          } catch (commitErr) {
            if (this.config.enableDebugLogs) {
              console.warn('⚠️ input_audio_buffer.commit failed (continuing):', commitErr);
            }
          }
          // Ensure session still prefers audio + text before creating a response
          this.client.updateSession({ modalities: ['text', 'audio'], output_audio_format: 'pcm16', voice: (this.config.voice as string) ?? 'alloy' });
          this.client.createResponse();
          this.hasPendingAudio = false;
        }
      } catch (respErr) {
        console.error('Error creating model response:', respErr);
      }

      // Disable mic track while not listening to avoid echo-triggered interrupts
      if (this.micTrack) {
        try { this.micTrack.enabled = false; } catch (error) {
          console.warn('Error disabling mic track:', error);
        }
      }

      // Tear down processing nodes
      if (this.micProcessorNode) {
        this.micProcessorNode.disconnect();
        this.micProcessorNode.onaudioprocess = null;
        this.micProcessorNode = null;
      }
      if (this.micWorkletNode) {
        try { this.micWorkletNode.port.onmessage = null; } catch (error) {
          console.warn('Error clearing worklet port message handler:', error);
        }
        this.micWorkletNode.disconnect();
        this.micWorkletNode = null;
      }
      if (this.micSourceNode) {
        this.micSourceNode.disconnect();
        this.micSourceNode = null;
      }

      if (this.vadFallbackTimer) {
        clearInterval(this.vadFallbackTimer);
        this.vadFallbackTimer = null;
      }

      if (this.config.enableDebugLogs) {
        console.log('🛑 Stopped listening (mic stream paused)');
      }
    } catch (error) {
      console.error('Error stopping listening:', error);
      this.isListening = false; // Force state update even on error
    }
  }

  /**
   * Send a text message (for testing or fallback)
   */
  sendTextMessage(text: string): void {
    if (!this.isConnected) {
      this.events.onError?.('Not connected to OpenAI Realtime API');
      return;
    }

    this.startTime = Date.now();
    this.client.sendUserMessageContent([{ type: 'input_text', text }]);
    
    if (this.config.enableDebugLogs) {
      console.log('📝 Sent text message:', text);
    }
  }

  /**
   * Interrupt the current response
   */
  interrupt(): void {
    if (this.isConnected) {
      // The RealtimeClient handles interruption automatically via server VAD
      // We just need to signal that we're listening again
      this.events.onListening?.();
    }
  }

  /**
   * Get current connection status
   */
  getStatus(): {
    isConnected: boolean;
    isListening: boolean;
  } {
    return {
      isConnected: this.isConnected,
      isListening: this.isListening,
    };
  }

  /**
   * Update session configuration
   */
  updateConfig(updates: Partial<VoiceClientConfig>): void {
    this.config = { ...this.config, ...updates };
    
    // WS: update session directly; WebRTC: send on datachannel
    const applySessionUpdate = (sessionPatch: Record<string, unknown>) => {
      if (this.config.transport === 'webrtc') {
        this.sendRtcEvent('session.update', { session: sessionPatch });
      } else {
        this.client.updateSession(sessionPatch);
      }
    };

    if (updates.voice ?? updates.temperature) {
      applySessionUpdate({
        voice: this.config.voice,
        temperature: this.config.temperature,
      });
    }

    if (updates.silenceDurationMs !== undefined) {
      applySessionUpdate({
        turn_detection: {
          type: 'server_vad',
          threshold: 0.5,
          prefix_padding_ms: 300,
          silence_duration_ms: updates.silenceDurationMs,
        },
      });
    }
  }

  /**
   * Get conversation history
   */
  getConversationHistory(): Record<string, unknown>[] {
    return this.client.conversation.getItems();
  }

  /**
   * OpenAI Best Practice: Monitor context length and manage token usage
   */
  getContextStats(): { itemCount: number; estimatedTokens: number; isNearLimit: boolean } {
    const items = this.getConversationHistory();
    const estimatedTokens = items.reduce((total, item) => {
      // Rough estimation: 1 token per 4 characters for text, 800 tokens per minute for audio
      if (item.type === 'message') {
        const textLength = item.content?.reduce((len: number, content: { text?: string }) => {
          return len + (content.text?.length ?? 0);
        }, 0) ?? 0;
        return total + Math.ceil(textLength / 4);
      } else if (item.type === 'function_call') {
        return total + 50; // Rough estimate for function calls
      }
      return total + 100; // Default estimate for other types
    }, 0);

    const maxTokens = this.config.maxContextTokens ?? 128000;
    const isNearLimit = estimatedTokens > maxTokens * 0.8; // 80% threshold

    return {
      itemCount: items.length,
      estimatedTokens,
      isNearLimit
    };
  }

  /**
   * OpenAI Best Practice: Update latency metrics for performance monitoring
   */
  private updateLatencyMetrics(latencyMs: number): void {
    const metrics = this.performanceMetrics;
    metrics.averageLatency = (metrics.averageLatency * (metrics.totalRequests - 1) + latencyMs) / metrics.totalRequests;

    if (this.config.enableDebugLogs) {
      console.log(`📊 Latency: ${latencyMs}ms (avg: ${metrics.averageLatency.toFixed(0)}ms)`);
    }
  }

  /**
   * OpenAI Best Practice: Truncate conversation on interruption
   */
  private truncateCurrentResponse(audioPlayedDurationMs: number): void {
    if (!this.currentResponseId) return;

    try {
      // Calculate audio duration in seconds for truncation
      const audioPlayedSeconds = audioPlayedDurationMs / 1000;

      // Send truncation event to OpenAI
      this.client.realtime.send('conversation.item.truncate', {
        item_id: this.currentResponseId,
        content_index: 0,
        audio_end_ms: Math.floor(audioPlayedDurationMs)
      });

      if (this.config.enableDebugLogs) {
        console.log(`🔄 Truncated response ${this.currentResponseId} at ${audioPlayedSeconds.toFixed(2)}s`);
      }

      // Reset tracking variables
      this.currentResponseId = null;
      this.audioStartTime = null;

    } catch (error) {
      console.error('Failed to truncate conversation:', error);
    }
  }

  /**
   * Clear conversation history
   */
  clearConversation(): void {
    // Note: The RealtimeClient doesn't have a built-in clear method
    // This would require disconnecting and reconnecting
    if (this.config.enableDebugLogs) {
      console.log('🗑️ Conversation clear requested (requires reconnection)');
    }
  }
}

/**
 * Factory function to create a RealtimeVoiceClient with default configuration
 */
export function createRealtimeVoiceClient(
  config: VoiceClientConfig,
  events: Partial<VoiceClientEvents> = {}
): RealtimeVoiceClient {
  return new RealtimeVoiceClient(config, events);
}
