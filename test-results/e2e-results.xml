<testsuites id="" name="" tests="2" failures="0" skipped="0" errors="0" time="7.863708">
<testsuite name="test-voice-button.spec.ts" timestamp="2025-09-21T04:10:36.242Z" hostname="chromium" tests="2" failures="0" skipped="0" time="13.172" errors="0">
<testcase name="floating voice button opens RealtimeVoiceAssistant" classname="test-voice-button.spec.ts" time="6.272">
<system-out>
<![CDATA[✅ Floating voice button successfully opens RealtimeVoiceAssistant!
]]>
</system-out>
</testcase>
<testcase name="voice assistant connect button functionality" classname="test-voice-button.spec.ts" time="6.9">
<system-out>
<![CDATA[Connection status: Connected
✅ Voice assistant connected successfully!
]]>
</system-out>
</testcase>
</testsuite>
</testsuites>